import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = createServiceSupabaseClient()
    const results = []

    // 1. Check if we can access storage
    try {
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()
      
      if (listError) {
        return NextResponse.json({
          success: false,
          error: `Cannot access storage: ${listError.message}`,
          message: "Storage access denied. Please check your Supabase configuration."
        })
      }

      results.push({
        step: "Check storage access",
        success: true,
        message: `Found ${buckets?.length || 0} existing buckets`
      })

      // 2. Check if required buckets exist
      const generalBucket = buckets?.find(bucket => bucket.name === 'general')
      const avatarsBucket = buckets?.find(bucket => bucket.name === 'avatars')

      if (generalBucket) {
        results.push({
          step: "Check general bucket",
          success: true,
          message: "General bucket already exists"
        })
      } else {
        // 3. Try to create bucket (this might fail due to RLS)
        try {
          const { error: createError } = await supabase.storage.createBucket('general', {
            public: true,
            fileSizeLimit: 10485760, // 10MB
          })

          if (createError) {
            results.push({
              step: "Create general bucket",
              success: false,
              error: createError.message,
              needsManualSetup: true
            })
          } else {
            results.push({
              step: "Create general bucket",
              success: true,
              message: "General bucket created successfully"
            })
          }
        } catch (createErr) {
          results.push({
            step: "Create general bucket",
            success: false,
            error: createErr instanceof Error ? createErr.message : "Unknown error",
            needsManualSetup: true
          })
        }
      }

      // 3.5. Create avatars bucket if needed
      if (avatarsBucket) {
        results.push({
          step: "Check avatars bucket",
          success: true,
          message: "Avatars bucket already exists"
        })
      } else {
        try {
          const { error: createError } = await supabase.storage.createBucket('avatars', {
            public: true,
            fileSizeLimit: 10485760, // 10MB
          })

          if (createError) {
            results.push({
              step: "Create avatars bucket",
              success: false,
              error: createError.message,
              needsManualSetup: true
            })
          } else {
            results.push({
              step: "Create avatars bucket",
              success: true,
              message: "Avatars bucket created successfully"
            })
          }
        } catch (createErr) {
          results.push({
            step: "Create avatars bucket",
            success: false,
            error: createErr instanceof Error ? createErr.message : "Unknown error",
            needsManualSetup: true
          })
        }
      }

      // 4. Test upload to check permissions
      try {
        const testFile = new Blob(['test'], { type: 'text/plain' })
        const testFileName = `test-${Date.now()}.txt`
        
        const { error: uploadError } = await supabase.storage
          .from('general')
          .upload(testFileName, testFile)

        if (uploadError) {
          results.push({
            step: "Test upload",
            success: false,
            error: uploadError.message,
            needsPolicySetup: true
          })
        } else {
          results.push({
            step: "Test upload",
            success: true,
            message: "Upload test successful"
          })

          // Clean up test file
          await supabase.storage.from('general').remove([testFileName])
        }
      } catch (uploadErr) {
        results.push({
          step: "Test upload",
          success: false,
          error: uploadErr instanceof Error ? uploadErr.message : "Unknown error",
          needsPolicySetup: true
        })
      }

    } catch (error) {
      results.push({
        step: "Storage setup",
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      })
    }

    const successCount = results.filter(r => r.success).length
    const needsManualSetup = results.some(r => r.needsManualSetup)
    const needsPolicySetup = results.some(r => r.needsPolicySetup)

    return NextResponse.json({
      success: successCount > 0,
      message: needsManualSetup || needsPolicySetup
        ? "Storage setup needs manual configuration"
        : `Storage setup completed: ${successCount}/${results.length} steps successful`,
      results,
      needsManualSetup,
      needsPolicySetup,
      manualInstructions: (needsManualSetup || needsPolicySetup) ? [
        "Go to Supabase Dashboard > Storage",
        "Create 'general' bucket if it doesn't exist",
        "Set bucket to public",
        "Go to Storage > Policies",
        "Create policies for SELECT, INSERT, UPDATE, DELETE operations",
        "Allow public access or authenticated users as needed"
      ] : null
    })

  } catch (error) {
    console.error("Storage setup error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to setup storage"
      },
      { status: 500 }
    )
  }
}
