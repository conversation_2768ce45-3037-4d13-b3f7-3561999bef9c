import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "portfolio"

    const supabase = await createServerSupabaseClient()

    // Kiểm tra quyền truy cập Storage API
    try {
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()

      if (listError) {
        console.error("Lỗi khi liệt kê buckets:", listError)
        return NextResponse.json(
          {
            success: false,
            error: `Không có quyền truy cập Storage API: ${listError.message}`,
            step: "check_storage_access",
          },
          { status: 500 },
        )
      }

      console.log(`Đã liệt kê được ${buckets?.length || 0} buckets`)

      // Kiểm tra xem bucket đã tồn tại chưa
      const bucketExists = buckets?.some((bucket) => bucket.name === bucketName)

      if (!bucketExists) {
        // Tạo bucket mới
        console.log(`Bucket '${bucketName}' chưa tồn tại, đang tạo mới...`)

        const { error: createError } = await supabase.storage.createBucket(bucketName, {
          public: true,
          fileSizeLimit: 10485760, // 10MB
        })

        if (createError) {
          console.error("Lỗi khi tạo bucket:", createError)
          return NextResponse.json(
            {
              success: false,
              error: `Không thể tạo bucket: ${createError.message}`,
              step: "create_bucket",
            },
            { status: 500 },
          )
        }

        console.log(`Đã tạo bucket '${bucketName}' thành công`)
      } else {
        // Cập nhật quyền cho bucket
        console.log(`Bucket '${bucketName}' đã tồn tại, đang cập nhật quyền...`)

        const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
          public: true,
          fileSizeLimit: 10485760, // 10MB
        })

        if (updateError) {
          console.error("Lỗi khi cập nhật quyền bucket:", updateError)
          return NextResponse.json(
            {
              success: false,
              error: `Không thể cập nhật quyền bucket: ${updateError.message}`,
              step: "update_bucket",
            },
            { status: 500 },
          )
        }

        console.log(`Đã cập nhật quyền cho bucket '${bucketName}' thành công`)
      }

      // Thử upload một file test để xác nhận quyền ghi
      const testContent = `Test file created at ${new Date().toISOString()}`
      const testFileName = `fix-test-${Date.now()}.txt`
      const filePath = `test/${testFileName}`

      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, new Blob([testContent]), { upsert: true })

      if (uploadError) {
        console.error("Lỗi khi upload file test:", uploadError)
        return NextResponse.json(
          {
            success: false,
            error: `Không thể upload file test: ${uploadError.message}`,
            step: "test_upload",
          },
          { status: 500 },
        )
      }

      console.log(`Đã upload file test thành công: ${filePath}`)

      // Lấy URL công khai
      const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)

      return NextResponse.json({
        success: true,
        message: `Đã sửa lỗi Storage thành công. Bucket '${bucketName}' đã sẵn sàng sử dụng.`,
        testFile: {
          filePath,
          publicUrl: urlData.publicUrl,
        },
      })
    } catch (error) {
      console.error("Lỗi khi sửa lỗi Storage:", error)
      return NextResponse.json(
        {
          success: false,
          error: `Lỗi khi sửa lỗi Storage: ${error instanceof Error ? error.message : "Lỗi không xác định"}`,
          step: "exception",
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Lỗi trong API fix-storage:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
