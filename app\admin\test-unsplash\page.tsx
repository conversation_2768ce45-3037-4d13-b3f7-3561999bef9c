"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RefreshCw, Image as ImageIcon, Sparkles, Info } from "lucide-react"
import UnsplashImagePicker from "@/components/unsplash-image-picker"
import SmartUnsplashPicker from "@/components/smart-unsplash-picker"

export default function TestUnsplashPage() {
  const [selectedImage, setSelectedImage] = useState<string>("")
  const [selectedImageData, setSelectedImageData] = useState<any>(null)
  
  // Mock project data for testing Smart Picker
  const [projectData, setProjectData] = useState({
    title: "React Portfolio Website",
    description: "A modern portfolio website built with React and TypeScript featuring responsive design and smooth animations",
    tags: ["react", "typescript", "web", "portfolio", "frontend"]
  })

  const handleImageSelected = (url: string, imageData?: any) => {
    setSelectedImage(url)
    setSelectedImageData(imageData)
  }

  const handleProjectDataChange = (field: string, value: string | string[]) => {
    setProjectData(prev => ({ ...prev, [field]: value }))
  }

  const addTag = (tag: string) => {
    if (tag && !projectData.tags.includes(tag)) {
      setProjectData(prev => ({ 
        ...prev, 
        tags: [...prev.tags, tag] 
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setProjectData(prev => ({ 
      ...prev, 
      tags: prev.tags.filter(tag => tag !== tagToRemove) 
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Test Unsplash Integration</h1>
        <p className="text-gray-500 mt-2">
          Test the Unsplash image picker components for project images
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> If Unsplash API key is not configured, fallback images from Picsum Photos will be used.
          To use real Unsplash images, add your <code>UNSPLASH_ACCESS_KEY</code> to environment variables.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Data Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Project Data (for Smart Picker)
            </CardTitle>
            <CardDescription>
              Configure project data to test smart suggestions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                value={projectData.title}
                onChange={(e) => handleProjectDataChange("title", e.target.value)}
                placeholder="Enter project title..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Project Description</Label>
              <Textarea
                id="description"
                value={projectData.description}
                onChange={(e) => handleProjectDataChange("description", e.target.value)}
                placeholder="Enter project description..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Project Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {projectData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeTag(tag)}
                  >
                    {tag} ×
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag..."
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      addTag(e.currentTarget.value)
                      e.currentTarget.value = ""
                    }
                  }}
                />
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {["javascript", "python", "mobile", "ai", "blockchain", "ecommerce"].map((suggestion) => (
                  <Button
                    key={suggestion}
                    variant="outline"
                    size="sm"
                    onClick={() => addTag(suggestion)}
                    className="text-xs"
                  >
                    + {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Selected Image Display */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Selected Image
            </CardTitle>
            <CardDescription>
              Currently selected image and metadata
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedImage ? (
              <div className="space-y-4">
                <div className="relative aspect-video overflow-hidden rounded-lg border border-gray-200">
                  <img
                    src={selectedImage}
                    alt="Selected"
                    className="h-full w-full object-cover"
                  />
                </div>
                
                <div className="space-y-2 text-sm">
                  <div>
                    <strong>URL:</strong>
                    <p className="font-mono text-xs break-all bg-gray-50 p-2 rounded mt-1">
                      {selectedImage}
                    </p>
                  </div>
                  
                  {selectedImageData && (
                    <div>
                      <strong>Metadata:</strong>
                      <details className="mt-1">
                        <summary className="cursor-pointer text-blue-600">View image data</summary>
                        <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto max-h-32">
                          {JSON.stringify(selectedImageData, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>

                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedImage("")
                    setSelectedImageData(null)
                  }}
                  className="w-full"
                >
                  Clear Selection
                </Button>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No image selected</p>
                <p className="text-xs">Choose an image from the pickers below</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Image Picker Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Image Picker Components</CardTitle>
          <CardDescription>
            Test both basic and smart Unsplash image pickers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="smart" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="smart">Smart Picker</TabsTrigger>
              <TabsTrigger value="basic">Basic Picker</TabsTrigger>
            </TabsList>
            
            <TabsContent value="smart" className="mt-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Sparkles className="h-4 w-4" />
                  <span>Smart picker analyzes project data to suggest relevant search terms</span>
                </div>
                <SmartUnsplashPicker
                  onImageSelected={handleImageSelected}
                  currentImageUrl={selectedImage}
                  projectTitle={projectData.title}
                  projectDescription={projectData.description}
                  projectTags={projectData.tags}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="basic" className="mt-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <ImageIcon className="h-4 w-4" />
                  <span>Basic picker with manual search</span>
                </div>
                <UnsplashImagePicker
                  onImageSelected={handleImageSelected}
                  currentImageUrl={selectedImage}
                  searchPlaceholder="Search for any images..."
                  defaultQuery="technology"
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
