import { ensureStorageBucket } from "@/utils/supabase/storage-utils"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    // Parse the request body
    let bucketName = "portfolio"
    try {
      const body = await request.json()
      bucketName = body.bucketName || "portfolio"
    } catch (e) {
      // If parsing fails, use default bucket name
      console.log("Không thể phân tích body request, sử dụng tên bucket mặc định")
    }

    console.log(`Đang đảm bảo bucket '${bucketName}' tồn tại...`)

    const result = await ensureStorageBucket(bucketName)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: `Bucket '${bucketName}' đã sẵn sàng`,
        warning: result.warning,
      })
    } else {
      console.error("Không thể đảm bảo bucket tồn tại:", result.error)
      return NextResponse.json(
        {
          success: false,
          message: "Không thể đảm bảo bucket tồn tại",
          error: result.error,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Lỗi trong route ensure-bucket:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Đã xảy ra lỗi không mong muốn",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
