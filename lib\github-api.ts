// GitHub API service for fetching real data

export interface GitHubUser {
  login: string
  id: number
  avatar_url: string
  name: string | null
  company: string | null
  blog: string | null
  location: string | null
  email: string | null
  bio: string | null
  public_repos: number
  public_gists: number
  followers: number
  following: number
  created_at: string
  updated_at: string
}

export interface GitHubRepo {
  id: number
  name: string
  full_name: string
  description: string | null
  html_url: string
  homepage: string | null
  language: string | null
  stargazers_count: number
  watchers_count: number
  forks_count: number
  open_issues_count: number
  created_at: string
  updated_at: string
  pushed_at: string
  topics: string[]
  visibility: string
  default_branch: string
}

export interface GitHubStats {
  user: GitHubUser
  repos: GitHubRepo[]
  totalStars: number
  totalForks: number
  mostStarredRepo: GitHubRepo | null
  recentRepos: GitHubRepo[]
  languages: { [key: string]: number }
  topLanguages: string[]
}

class GitHubAPI {
  private baseURL = 'https://api.github.com'
  private token?: string

  constructor(token?: string) {
    this.token = token
  }

  private async fetch(endpoint: string): Promise<any> {
    const headers: HeadersInit = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'Portfolio-App'
    }

    if (this.token) {
      headers['Authorization'] = `token ${this.token}`
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, { headers })

    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  async getUser(username: string): Promise<GitHubUser> {
    return this.fetch(`/users/${username}`)
  }

  async getUserRepos(username: string, options: {
    sort?: 'created' | 'updated' | 'pushed' | 'full_name'
    direction?: 'asc' | 'desc'
    per_page?: number
    page?: number
  } = {}): Promise<GitHubRepo[]> {
    const params = new URLSearchParams({
      sort: options.sort || 'updated',
      direction: options.direction || 'desc',
      per_page: (options.per_page || 100).toString(),
      page: (options.page || 1).toString()
    })

    return this.fetch(`/users/${username}/repos?${params}`)
  }

  async getRepoLanguages(username: string, repoName: string): Promise<{ [key: string]: number }> {
    return this.fetch(`/repos/${username}/${repoName}/languages`)
  }

  async getUserStats(username: string): Promise<GitHubStats> {
    try {
      // Fetch user data
      const user = await this.getUser(username)
      
      // Fetch repositories
      const repos = await this.getUserRepos(username, { 
        sort: 'updated', 
        per_page: 100 
      })

      // Calculate stats
      const totalStars = repos.reduce((sum, repo) => sum + repo.stargazers_count, 0)
      const totalForks = repos.reduce((sum, repo) => sum + repo.forks_count, 0)
      
      // Find most starred repo
      const mostStarredRepo = repos.reduce((max, repo) => 
        repo.stargazers_count > (max?.stargazers_count || 0) ? repo : max, 
        null as GitHubRepo | null
      )

      // Get recent repos (last 6 months)
      const sixMonthsAgo = new Date()
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
      
      const recentRepos = repos.filter(repo => 
        new Date(repo.updated_at) > sixMonthsAgo
      ).slice(0, 10)

      // Aggregate languages
      const languages: { [key: string]: number } = {}
      repos.forEach(repo => {
        if (repo.language) {
          languages[repo.language] = (languages[repo.language] || 0) + 1
        }
      })

      // Get top languages
      const topLanguages = Object.entries(languages)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([lang]) => lang)

      return {
        user,
        repos,
        totalStars,
        totalForks,
        mostStarredRepo,
        recentRepos,
        languages,
        topLanguages
      }
    } catch (error) {
      console.error('Error fetching GitHub stats:', error)
      throw error
    }
  }

  // Get rate limit info
  async getRateLimit(): Promise<any> {
    return this.fetch('/rate_limit')
  }
}

// Create singleton instance
export const githubAPI = new GitHubAPI(process.env.GITHUB_TOKEN)

// Helper functions
export function formatGitHubDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export function getRepoTopics(repo: GitHubRepo): string[] {
  return repo.topics || []
}

export function isRepoRecent(repo: GitHubRepo, months: number = 6): boolean {
  const cutoffDate = new Date()
  cutoffDate.setMonth(cutoffDate.getMonth() - months)
  return new Date(repo.updated_at) > cutoffDate
}

export function getLanguageColor(language: string): string {
  const colors: { [key: string]: string } = {
    'JavaScript': '#f1e05a',
    'TypeScript': '#2b7489',
    'Python': '#3572A5',
    'Java': '#b07219',
    'C++': '#f34b7d',
    'C#': '#239120',
    'PHP': '#4F5D95',
    'Ruby': '#701516',
    'Go': '#00ADD8',
    'Rust': '#dea584',
    'Swift': '#ffac45',
    'Kotlin': '#F18E33',
    'Dart': '#00B4AB',
    'HTML': '#e34c26',
    'CSS': '#1572B6',
    'Vue': '#4FC08D',
    'React': '#61DAFB',
    'Angular': '#DD0031'
  }
  
  return colors[language] || '#586069'
}
