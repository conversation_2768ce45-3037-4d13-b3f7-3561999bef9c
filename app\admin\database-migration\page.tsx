"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Check, AlertCircle, Database, RefreshCw } from "lucide-react"

export default function DatabaseMigrationPage() {
  const [isRunningMigration, setIsRunningMigration] = useState(false)
  const [migrationResult, setMigrationResult] = useState<{
    success: boolean
    message: string
    error?: string
  } | null>(null)

  const runMigration = async () => {
    setIsRunningMigration(true)
    setMigrationResult(null)

    try {
      // Use simple seed instead of complex migration
      const response = await fetch("/api/simple-seed", {
        method: "POST",
      })

      const result = await response.json()
      setMigrationResult(result)

    } catch (error) {
      console.error("Migration error:", error)
      setMigrationResult({
        success: false,
        message: "Failed to run migration",
        error: error instanceof Error ? error.message : "Unknown error"
      })
    } finally {
      setIsRunningMigration(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Database Migration</h1>
        <p className="text-gray-500 mt-2">
          Update database schema and ensure all required tables and columns exist
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Setup
          </CardTitle>
          <CardDescription>
            This will seed your database with sample data to ensure the portfolio displays properly.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600">
            <p className="mb-2">This setup will:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Add sample personal information if table is empty</li>
              <li>Insert technology skills and expertise</li>
              <li>Create sample projects with tags</li>
              <li>Add social media links</li>
              <li>Insert GitHub statistics</li>
              <li>Skip tables that already have data</li>
            </ul>
          </div>

          {migrationResult && (
            <Alert className={migrationResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
              {migrationResult.success ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={migrationResult.success ? "text-green-800" : "text-red-800"}>
                {migrationResult.message}
                {migrationResult.error && (
                  <div className="mt-2 text-sm">
                    <strong>Error:</strong> {migrationResult.error}
                  </div>
                )}
                {migrationResult.summary?.needsTableCreation && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-yellow-800 font-medium">Tables need to be created first:</p>
                    <p className="text-yellow-700 text-sm mt-1">
                      Please create the missing tables in your Supabase dashboard, then run this setup again.
                    </p>
                    <div className="mt-2">
                      <a
                        href="/admin/manual-setup"
                        className="text-yellow-800 underline text-sm hover:text-yellow-900"
                      >
                        View manual setup instructions →
                      </a>
                    </div>
                  </div>
                )}
                {migrationResult.results && (
                  <div className="mt-3">
                    <details className="text-sm">
                      <summary className="cursor-pointer font-medium">View detailed results</summary>
                      <div className="mt-2 space-y-1">
                        {migrationResult.results.map((result: any, index: number) => (
                          <div key={index} className={`p-2 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}`}>
                            <span className="font-medium">{result.table}:</span> {result.message || result.error}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={runMigration}
            disabled={isRunningMigration}
            className="w-full"
          >
            {isRunningMigration ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Setting up Database...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Setup Database with Sample Data
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Migration Details</CardTitle>
          <CardDescription>
            Technical details about what this migration does
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Tables that will be created/updated:</h4>
            <ul className="list-disc list-inside space-y-1 ml-4 text-gray-600">
              <li><code>personal_info</code> - Personal information and profile data</li>
              <li><code>technologies</code> - Technology skills and expertise</li>
              <li><code>projects</code> - Portfolio projects</li>
              <li><code>project_tags</code> - Tags associated with projects</li>
              <li><code>social_links</code> - Social media and external links</li>
              <li><code>github_stats</code> - GitHub statistics</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
