"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Check, AlertCircle, Database, RefreshCw } from "lucide-react"

export default function DatabaseMigrationPage() {
  const [isRunningMigration, setIsRunningMigration] = useState(false)
  const [migrationResult, setMigrationResult] = useState<{
    success: boolean
    message: string
    error?: string
  } | null>(null)

  const runMigration = async () => {
    setIsRunningMigration(true)
    setMigrationResult(null)

    try {
      const response = await fetch("/api/migrate-database", {
        method: "POST",
      })

      const result = await response.json()
      setMigrationResult(result)

      if (result.success) {
        // Also run seeding after successful migration
        setTimeout(async () => {
          try {
            const seedResponse = await fetch("/api/seed-database", {
              method: "POST",
            })
            const seedResult = await seedResponse.json()
            
            if (seedResult.message === "Database already has data") {
              setMigrationResult({
                success: true,
                message: "Migration completed successfully. Database already contains data."
              })
            } else {
              setMigrationResult({
                success: true,
                message: "Migration and seeding completed successfully!"
              })
            }
          } catch (seedError) {
            console.error("Seeding error:", seedError)
            setMigrationResult({
              success: true,
              message: "Migration completed, but seeding failed. You may need to run seeding manually."
            })
          }
        }, 1000)
      }
    } catch (error) {
      console.error("Migration error:", error)
      setMigrationResult({
        success: false,
        message: "Failed to run migration",
        error: error instanceof Error ? error.message : "Unknown error"
      })
    } finally {
      setIsRunningMigration(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Database Migration</h1>
        <p className="text-gray-500 mt-2">
          Update database schema and ensure all required tables and columns exist
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Schema Migration
          </CardTitle>
          <CardDescription>
            This will update your database schema to ensure all required tables and columns exist for the portfolio application.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600">
            <p className="mb-2">This migration will:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Add missing <code>profile_image</code> column to personal_info table</li>
              <li>Ensure all required tables exist with proper structure</li>
              <li>Create necessary indexes for better performance</li>
              <li>Add sample data if tables are empty</li>
              <li>Set up proper foreign key relationships</li>
            </ul>
          </div>

          {migrationResult && (
            <Alert className={migrationResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
              {migrationResult.success ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={migrationResult.success ? "text-green-800" : "text-red-800"}>
                {migrationResult.message}
                {migrationResult.error && (
                  <div className="mt-2 text-sm">
                    <strong>Error:</strong> {migrationResult.error}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          <Button 
            onClick={runMigration} 
            disabled={isRunningMigration}
            className="w-full"
          >
            {isRunningMigration ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Running Migration...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Run Database Migration
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Migration Details</CardTitle>
          <CardDescription>
            Technical details about what this migration does
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <h4 className="font-semibold">Tables that will be created/updated:</h4>
            <ul className="list-disc list-inside space-y-1 ml-4 text-gray-600">
              <li><code>personal_info</code> - Personal information and profile data</li>
              <li><code>technologies</code> - Technology skills and expertise</li>
              <li><code>projects</code> - Portfolio projects</li>
              <li><code>project_tags</code> - Tags associated with projects</li>
              <li><code>social_links</code> - Social media and external links</li>
              <li><code>github_stats</code> - GitHub statistics</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
