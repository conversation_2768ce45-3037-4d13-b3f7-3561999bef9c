# Hướng dẫn Kiểm tra và Cập nhật Database

## Tổng quan

Tài liệu này hướng dẫn cách kiểm tra và cập nhật cấu trúc database Supabase để đảm bảo trang chủ hiển thị đầy đủ thông tin.

## Vấn đề đã được khắc phục

### 1. **Thiếu trường `profile_image`**
- **Vấn đề**: Bảng `personal_info` thiếu trường `profile_image` để hiển thị ảnh profile trên trang chủ
- **Giải pháp**: Đã thêm trường `profile_image TEXT` vào bảng `personal_info`

### 2. **Cấu trúc bảng không đồng nhất**
- **Vấn đề**: C<PERSON> sự khác biệt giữa schema trong `types/supabase.ts` và cấu trúc thực tế
- **Gi<PERSON>i pháp**: Đ<PERSON> cập nhật và đồng bộ hóa tất cả các file schema

### 3. **Thiếu dữ liệu mẫu**
- **Vấn đề**: Database có thể trống, không có dữ liệu để hiển thị
- **Giải pháp**: Đã tạo migration script với dữ liệu mẫu đầy đủ

## Cấu trúc Database Mới

### Bảng `personal_info`
```sql
CREATE TABLE personal_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    bio TEXT,
    location TEXT,
    website TEXT,
    email TEXT NOT NULL,
    profile_image TEXT,  -- ← Trường mới được thêm
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Bảng `technologies`
```sql
CREATE TABLE technologies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Bảng `projects`
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Bảng `project_tags`
```sql
CREATE TABLE project_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, tag)
);
```

### Bảng `social_links`
```sql
CREATE TABLE social_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform TEXT NOT NULL,
    url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(platform)
);
```

### Bảng `github_stats`
```sql
CREATE TABLE github_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stars INTEGER DEFAULT 0,
    repos INTEGER DEFAULT 0,
    followers INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Cách sử dụng

### 1. **Kiểm tra trạng thái Database**
1. Truy cập `/admin/dashboard`
2. Xem card "Database Status" để kiểm tra tình trạng database
3. Nếu có vấn đề, sẽ hiển thị "Needs Attention"

### 2. **Chạy Migration**
1. Truy cập `/admin/database-migration`
2. Nhấn nút "Run Database Migration"
3. Migration sẽ:
   - Tạo các bảng nếu chưa tồn tại
   - Thêm các trường thiếu
   - Tạo indexes để tối ưu hiệu suất
   - Thêm dữ liệu mẫu nếu bảng trống

### 3. **Seed dữ liệu (nếu cần)**
1. Truy cập `/admin/seed-database`
2. Nhấn "Initialize Database" để thêm dữ liệu mẫu

## API Endpoints mới

### `POST /api/migrate-database`
- Chạy migration script để cập nhật schema
- Tự động thêm dữ liệu mẫu nếu cần

### `GET /api/check-database`
- Kiểm tra trạng thái database
- Trả về thông tin về các bảng và dữ liệu

## Dữ liệu mẫu

Migration sẽ tự động thêm:

### Personal Info
- Name: "Peanut"
- Role: "Creative developer currently working on Blameo"
- Bio: Mô tả chi tiết về kinh nghiệm
- Location: "Hanoi"
- Website: "truongqa.com"
- Email: "<EMAIL>"
- Profile Image: Placeholder image

### Technologies
24 công nghệ phổ biến: React, Next.js, TypeScript, JavaScript, Node.js, Vue.js, HTML5, CSS, Tailwind CSS, Redux, GraphQL, AWS, Firebase, Docker, Git, MongoDB, Express.js, Vercel, Chakra UI, Figma, Styled-components, Sass, Bootstrap, PostgreSQL

### Social Links
- GitHub: https://github.com/truongqa
- Website: https://truongqa.com
- Medium: https://medium.com/@truongqa
- StackOverflow: https://stackoverflow.com/users/truongqa

### GitHub Stats
- Stars: 42
- Repositories: 15
- Followers: 28

### Sample Projects
6 dự án mẫu với tags và mô tả đầy đủ

## Kiểm tra kết quả

Sau khi chạy migration:

1. **Trang chủ** (`/`) sẽ hiển thị:
   - ✅ Thông tin cá nhân đầy đủ
   - ✅ Ảnh profile (nếu có)
   - ✅ Danh sách technologies
   - ✅ GitHub statistics
   - ✅ Các dự án với tags
   - ✅ Social links
   - ✅ Form liên hệ

2. **Admin Dashboard** (`/admin/dashboard`) sẽ hiển thị:
   - ✅ Database Status: "Healthy"
   - ✅ Số lượng bảng: 6/6
   - ✅ Không có issues

## Troubleshooting

### Nếu migration thất bại:
1. Kiểm tra Supabase connection
2. Đảm bảo có quyền admin
3. Kiểm tra logs trong browser console
4. Thử chạy lại migration

### Nếu dữ liệu không hiển thị:
1. Kiểm tra `/api/check-database`
2. Refresh data trong admin dashboard
3. Kiểm tra fallback data có hoạt động không

### Nếu có lỗi TypeScript:
1. Restart development server
2. Kiểm tra `types/supabase.ts` đã được cập nhật
3. Clear Next.js cache: `rm -rf .next`

## Kết luận

Với các cập nhật này, trang chủ portfolio sẽ hiển thị đầy đủ thông tin từ database Supabase. Hệ thống cũng có fallback data để đảm bảo trang web luôn hoạt động ngay cả khi có vấn đề với database.
