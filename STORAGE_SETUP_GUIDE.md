# Hướng dẫn khắc phục lỗi Supabase Storage

## Tổng quan

Tài liệu này hướng dẫn khắc phục các lỗi liên quan đến Supabase Storage, đặc biệt là lỗi RLS (Row Level Security).

## Các lỗi phổ biến

### 1. "new row violates row-level security policy"

**Nguyên nhân:** RLS đang chặn việc tạo bucket hoặc upload file.

**Giải pháp:**

#### Cách 1: Tạo Storage Policies (Khuyến nghị)

1. **Truy cập Supabase Dashboard:**
   - <PERSON><PERSON> đến https://supabase.com/dashboard
   - Chọn project của bạn
   - Vào **Storage** → **Policies**

2. **Tạo policies cho bucket 'general':**

```sql
-- Policy cho SELECT (đọc file)
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'general');

-- Policy cho INSERT (upload file)
CREATE POLICY "Public Upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'general');

-- Policy cho UPDATE (cập nhật file)
CREATE POLICY "Public Update" ON storage.objects FOR UPDATE USING (bucket_id = 'general');

-- Policy cho DELETE (xóa file)
CREATE POLICY "Public Delete" ON storage.objects FOR DELETE USING (bucket_id = 'general');
```

#### Cách 2: Tạo bucket thủ công

1. **Tạo bucket:**
   - Vào **Storage** → **New bucket**
   - Name: `general`
   - Public: ✅ Yes
   - File size limit: 10MB
   - Click **Save**

2. **Cấu hình policies như Cách 1**

### 2. "supabase.query is not a function"

**Nguyên nhân:** Sử dụng method `.query()` không tồn tại trong Supabase client.

**Giải pháp:** Đã được sửa trong code, sử dụng `.rpc('exec_sql')` thay thế.

### 3. "Could not find the function public.exec_sql"

**Nguyên nhân:** Function `exec_sql` không có sẵn trong Supabase.

**Giải pháp:** Sử dụng SQL Editor thủ công thay vì API.

## Quy trình khắc phục

### Bước 1: Kiểm tra Storage Status
1. Truy cập `/admin/storage-setup`
2. Nhấn "Setup Storage"
3. Xem kết quả và lỗi

### Bước 2: Tạo bucket thủ công (nếu cần)
1. Vào Supabase Dashboard → Storage
2. Tạo bucket 'general' với cấu hình:
   - Public: Yes
   - File size limit: 10MB

### Bước 3: Cấu hình Storage Policies
1. Vào Storage → Policies
2. Tạo policies cho SELECT, INSERT, UPDATE, DELETE
3. Cho phép public access

### Bước 4: Test upload
1. Truy cập `/admin/test-upload`
2. Thử upload file để kiểm tra

## SQL Scripts để chạy thủ công

### Tạo Storage Policies

```sql
-- Enable RLS on storage.objects (nếu chưa có)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Tạo policies cho bucket 'general'
CREATE POLICY "Allow public read access" ON storage.objects FOR SELECT USING (bucket_id = 'general');

CREATE POLICY "Allow public insert access" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'general');

CREATE POLICY "Allow public update access" ON storage.objects FOR UPDATE USING (bucket_id = 'general');

CREATE POLICY "Allow public delete access" ON storage.objects FOR DELETE USING (bucket_id = 'general');
```

### Tạo bucket bằng SQL (nếu cần)

```sql
-- Tạo bucket 'general'
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('general', 'general', true, 10485760, NULL);
```

## Kiểm tra sau khi setup

### 1. Kiểm tra bucket tồn tại
```sql
SELECT * FROM storage.buckets WHERE name = 'general';
```

### 2. Kiểm tra policies
```sql
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';
```

### 3. Test upload qua API
- Truy cập `/admin/test-upload`
- Upload file thử nghiệm
- Kiểm tra file xuất hiện trong Storage

## Troubleshooting

### Lỗi "Permission denied"
- Kiểm tra service role key có đúng không
- Đảm bảo key có quyền storage

### Lỗi "Bucket already exists"
- Đây là lỗi tốt, bucket đã tồn tại
- Chỉ cần cấu hình policies

### Upload thành công nhưng không thể truy cập file
- Kiểm tra bucket có được set public không
- Kiểm tra SELECT policy có đúng không

## Kết luận

Sau khi hoàn thành setup:
1. Storage bucket 'general' sẽ tồn tại và public
2. Có thể upload/download file thông qua API
3. Trang admin có thể upload ảnh profile và project images
4. Trang chủ có thể hiển thị ảnh từ storage

Nếu vẫn gặp vấn đề, kiểm tra:
- Supabase service role key
- RLS policies
- Bucket configuration
- Network connectivity
