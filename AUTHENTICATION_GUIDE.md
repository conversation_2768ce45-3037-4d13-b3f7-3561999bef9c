# Hướng dẫn Authentication với Supabase

## Tổng quan

Portfolio admin giờ đây sử dụng Supabase Authentication thay vì password mặc định. Hệ thống hỗ trợ đầy đủ các tính năng authentication hiện đại.

## Tính năng Authentication

### ✅ **Đ<PERSON> implement:**

1. **Email/Password Authentication**
   - Đăng ký tài khoản mới
   - Đăng nhập với email/password
   - X<PERSON><PERSON> thực email tự động

2. **Password Reset**
   - Gửi email reset password
   - Trang reset password với token validation
   - Cập nhật password an toàn

3. **Session Management**
   - Tự động refresh token
   - Persistent login
   - Secure logout

4. **Admin Protection**
   - Tất cả routes `/admin/*` được bảo vệ
   - Redirect tự động đến login nếu chưa authenticate
   - Real-time auth state management

## C<PERSON>ch sử dụng

### **Lần đầu setup:**

1. **Truy cập trang login:**
   ```
   http://localhost:3001/admin/login
   ```

2. **Tạo tài khoản admin:**
   - Click tab "Sign Up"
   - Nhập email và password (tối thiểu 6 ký tự)
   - Click "Create Account"
   - Kiểm tra email để verify (nếu được yêu cầu)

3. **Đăng nhập:**
   - Click tab "Login"
   - Nhập email và password
   - Click "Sign In"

### **Quên password:**

1. **Reset password:**
   - Click tab "Reset"
   - Nhập email
   - Click "Send Reset Link"
   - Kiểm tra email và click link reset

2. **Cập nhật password:**
   - Nhập password mới (2 lần)
   - Click "Update Password"
   - Tự động redirect về login

## Cấu hình Supabase

### **Authentication Settings:**

1. **Truy cập Supabase Dashboard:**
   - Đi đến https://supabase.com/dashboard
   - Chọn project của bạn
   - Vào **Authentication** → **Settings**

2. **Cấu hình Email Templates:**
   - **Confirm signup**: Template xác nhận đăng ký
   - **Reset password**: Template reset password
   - **Magic link**: Template đăng nhập không password

3. **Site URL Configuration:**
   ```
   Site URL: http://localhost:3001
   Redirect URLs: 
   - http://localhost:3001/admin/reset-password
   - http://localhost:3001/admin/dashboard
   ```

### **Email Provider Setup:**

1. **SMTP Configuration:**
   - Vào **Authentication** → **Settings** → **SMTP Settings**
   - Cấu hình SMTP server (Gmail, SendGrid, etc.)
   - Test gửi email

2. **Hoặc sử dụng Supabase Email:**
   - Mặc định Supabase cung cấp email service
   - Giới hạn 3 emails/hour cho free tier
   - Upgrade plan để tăng limit

## Cấu trúc Code

### **Auth Context (`contexts/auth-context.tsx`):**
```typescript
interface AuthContextType {
  user: User | null
  supabaseUser: SupabaseUser | null
  login: (email: string, password: string) => Promise<{success: boolean, error?: string}>
  signup: (email: string, password: string) => Promise<{success: boolean, error?: string}>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<{success: boolean, error?: string}>
  isLoading: boolean
}
```

### **Protected Routes:**
- Tất cả routes `/admin/*` (trừ `/admin/login` và `/admin/reset-password`)
- Auto-redirect đến login nếu chưa authenticate
- Persistent session across browser refresh

### **User Information:**
```typescript
type User = {
  id: string           // Supabase user ID
  email: string        // Email address
  isAdmin: boolean     // Always true for authenticated users
  metadata?: any       // Additional user metadata
}
```

## Security Features

### **1. Secure Authentication:**
- Passwords được hash bởi Supabase
- JWT tokens với auto-refresh
- Secure HTTP-only cookies (nếu cấu hình)

### **2. Email Verification:**
- Tùy chọn verify email sau đăng ký
- Prevent unverified users từ login

### **3. Password Requirements:**
- Minimum 6 characters
- Có thể cấu hình thêm requirements trong Supabase

### **4. Rate Limiting:**
- Supabase tự động rate limit auth requests
- Prevent brute force attacks

## Troubleshooting

### **Lỗi "Invalid login credentials":**
- Kiểm tra email/password đúng chưa
- Đảm bảo account đã được tạo
- Kiểm tra email có verified chưa (nếu required)

### **Lỗi "Email not confirmed":**
- Kiểm tra inbox và spam folder
- Resend confirmation email từ Supabase Dashboard
- Tắt email confirmation requirement (dev only)

### **Reset password không hoạt động:**
- Kiểm tra SMTP configuration
- Verify redirect URL trong Supabase settings
- Check email templates

### **Session không persist:**
- Kiểm tra localStorage/cookies
- Verify Supabase client configuration
- Check network connectivity

### **Upload không hoạt động sau khi login:**
- **Nguyên nhân:** Server-side API không có user session
- **Giải pháp:** Đã được fix bằng cách sử dụng service role key
- **Debug:** Truy cập `/admin/auth-debug` để kiểm tra chi tiết

### **Storage permission denied:**
- Kiểm tra service role key có đúng không
- Verify bucket 'general' đã tồn tại
- Cấu hình storage policies nếu cần
- Sử dụng `/admin/storage-setup` để tự động setup

## Development vs Production

### **Development:**
- Site URL: `http://localhost:3001`
- Email confirmation: Optional
- Rate limiting: Relaxed

### **Production:**
- Site URL: Your production domain
- Email confirmation: Required
- Rate limiting: Strict
- HTTPS required
- Custom SMTP recommended

## Migration từ old auth

### **Automatic Migration:**
- Old password-based auth đã được remove
- Users cần tạo account mới với Supabase
- Không có data loss vì auth và portfolio data tách biệt

### **Admin Access:**
- Tất cả authenticated users đều có admin access
- Có thể implement role-based access sau này
- Current: Simple boolean `isAdmin: true`

## Kết luận

Hệ thống authentication mới:
- ✅ Secure và production-ready
- ✅ Không còn hardcoded passwords
- ✅ Full-featured auth flow
- ✅ Easy to extend và customize
- ✅ Integrated với Supabase ecosystem

**Next steps:**
1. Tạo admin account đầu tiên
2. Cấu hình email provider
3. Test full auth flow
4. Deploy to production với proper domain
