import { createServerSupaba<PERSON>Client } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()
    const results = []

    // SQL commands to create tables
    const createTableCommands = [
      {
        name: 'personal_info',
        sql: `
          CREATE TABLE IF NOT EXISTS personal_info (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            role TEXT NOT NULL,
            bio TEXT,
            location TEXT,
            website TEXT,
            email TEXT NOT NULL,
            profile_image TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'technologies',
        sql: `
          CREATE TABLE IF NOT EXISTS technologies (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'projects',
        sql: `
          CREATE TABLE IF NOT EXISTS projects (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            title TEXT NOT NULL,
            description TEXT,
            image_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'project_tags',
        sql: `
          CREATE TABLE IF NOT EXISTS project_tags (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            tag TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(project_id, tag)
          );
        `
      },
      {
        name: 'social_links',
        sql: `
          CREATE TABLE IF NOT EXISTS social_links (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            platform TEXT NOT NULL,
            url TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(platform)
          );
        `
      },
      {
        name: 'github_stats',
        sql: `
          CREATE TABLE IF NOT EXISTS github_stats (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            stars INTEGER DEFAULT 0,
            repos INTEGER DEFAULT 0,
            followers INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      }
    ]

    // Execute each CREATE TABLE command
    for (const command of createTableCommands) {
      try {
        const { error } = await supabase.rpc('exec_sql', { 
          sql: command.sql 
        })

        if (error) {
          // If exec_sql doesn't work, try alternative approach
          console.log(`exec_sql failed for ${command.name}, trying alternative...`)
          results.push({
            table: command.name,
            success: false,
            error: `exec_sql not available: ${error.message}`,
            needsManualCreation: true
          })
        } else {
          results.push({
            table: command.name,
            success: true,
            message: 'Table created successfully'
          })
        }
      } catch (err) {
        results.push({
          table: command.name,
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error',
          needsManualCreation: true
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const needsManual = results.some(r => r.needsManualCreation)

    return NextResponse.json({
      success: successCount > 0,
      message: needsManual 
        ? "Some tables need manual creation in Supabase Dashboard"
        : `Successfully created ${successCount}/${createTableCommands.length} tables`,
      results,
      needsManualCreation: needsManual,
      manualInstructions: needsManual ? [
        "Go to Supabase Dashboard > SQL Editor",
        "Run the SQL commands from database/migrations/001_update_schema.sql",
        "Or create tables manually using Table Editor"
      ] : null
    })

  } catch (error) {
    console.error("Create tables error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to create tables"
      },
      { status: 500 }
    )
  }
}
