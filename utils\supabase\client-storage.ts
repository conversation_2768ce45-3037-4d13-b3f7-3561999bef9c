export async function ensureClientBucket(bucketName = "portfolio") {
  try {
    // Thay vì kiểm tra bucket có tồn tại không, chúng ta sẽ luôn gọi API route
    // để đảm bảo bucket được tạo từ phía server
    try {
      const response = await fetch("/api/ensure-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API route không thể tạo bucket:", errorData)
        return false
      }

      return true
    } catch (error) {
      console.error("Lỗi khi gọi API route:", error)
      return false
    }
  } catch (error) {
    console.error("Lỗi khi kiểm tra bucket từ client:", error)
    return false
  }
}
