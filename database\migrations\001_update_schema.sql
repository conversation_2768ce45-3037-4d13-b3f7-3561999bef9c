-- Migration: Update database schema for portfolio
-- Date: 2024-01-XX
-- Description: Add missing columns and ensure proper schema

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Update personal_info table
-- Add profile_image column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'personal_info' 
        AND column_name = 'profile_image'
    ) THEN
        ALTER TABLE personal_info ADD COLUMN profile_image TEXT;
    END IF;
END $$;

-- Ensure all required columns exist in personal_info
DO $$ 
BEGIN
    -- Check and add missing columns
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'personal_info' 
        AND column_name = 'location'
    ) THEN
        ALTER TABLE personal_info ADD COLUMN location TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'personal_info' 
        AND column_name = 'website'
    ) THEN
        ALTER TABLE personal_info ADD COLUMN website TEXT;
    END IF;
END $$;

-- Create or update personal_info table with proper structure
CREATE TABLE IF NOT EXISTS personal_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    bio TEXT,
    location TEXT,
    website TEXT,
    email TEXT NOT NULL,
    profile_image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create or update technologies table
CREATE TABLE IF NOT EXISTS technologies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create or update projects table
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create or update project_tags table
CREATE TABLE IF NOT EXISTS project_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, tag)
);

-- Create or update social_links table
CREATE TABLE IF NOT EXISTS social_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform TEXT NOT NULL,
    url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(platform)
);

-- Create or update github_stats table
CREATE TABLE IF NOT EXISTS github_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stars INTEGER DEFAULT 0,
    repos INTEGER DEFAULT 0,
    followers INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_project_tags_project_id ON project_tags(project_id);
CREATE INDEX IF NOT EXISTS idx_technologies_name ON technologies(name);
CREATE INDEX IF NOT EXISTS idx_social_links_platform ON social_links(platform);

-- Insert default data if tables are empty
INSERT INTO personal_info (name, role, bio, location, website, email, profile_image)
SELECT 
    'Peanut',
    'Creative developer currently working on Blameo',
    'I''m currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I''m currently exploring DevOps.',
    'Hanoi',
    'truongqa.com',
    '<EMAIL>',
    '/placeholder.svg?height=500&width=500'
WHERE NOT EXISTS (SELECT 1 FROM personal_info);

-- Insert default technologies if table is empty
INSERT INTO technologies (name)
SELECT unnest(ARRAY[
    'React', 'Next', 'TypeScript', 'JavaScript', 'Node.js', 'Vue.js',
    'HTML5', 'CSS', 'Tailwindcss', 'Redux', 'GraphQL', 'AWS',
    'Firebase', 'Docker', 'Git', 'MongoDB', 'Express.js', 'Vercel',
    'Chakra', 'Figma', 'Styled-components', 'Sass', 'Bootstrap', 'Postgres'
])
WHERE NOT EXISTS (SELECT 1 FROM technologies);

-- Insert default social links if table is empty
INSERT INTO social_links (platform, url)
SELECT * FROM (VALUES
    ('GitHub', 'https://github.com/truongqa'),
    ('Website', 'https://truongqa.com'),
    ('Medium', 'https://medium.com/@truongqa'),
    ('StackOverflow', 'https://stackoverflow.com/users/truongqa')
) AS v(platform, url)
WHERE NOT EXISTS (SELECT 1 FROM social_links);

-- Insert default GitHub stats if table is empty
INSERT INTO github_stats (stars, repos, followers)
SELECT 42, 15, 28
WHERE NOT EXISTS (SELECT 1 FROM github_stats);

-- Insert sample projects if table is empty
DO $$
DECLARE
    project1_id UUID;
    project2_id UUID;
    project3_id UUID;
BEGIN
    IF NOT EXISTS (SELECT 1 FROM projects) THEN
        -- Insert projects and get their IDs
        INSERT INTO projects (title, description, image_url) VALUES
        ('E-commerce Platform', 'A modern e-commerce platform built with Next.js and Stripe integration', '/placeholder.svg?height=300&width=400')
        RETURNING id INTO project1_id;
        
        INSERT INTO projects (title, description, image_url) VALUES
        ('Task Management App', 'A collaborative task management application with real-time updates', '/placeholder.svg?height=300&width=400')
        RETURNING id INTO project2_id;
        
        INSERT INTO projects (title, description, image_url) VALUES
        ('Portfolio Website', 'A responsive portfolio website showcasing my work and skills', '/placeholder.svg?height=300&width=400')
        RETURNING id INTO project3_id;
        
        -- Insert tags for projects
        INSERT INTO project_tags (project_id, tag) VALUES
        (project1_id, 'React'),
        (project1_id, 'Next.js'),
        (project1_id, 'TypeScript'),
        (project1_id, 'Stripe'),
        (project2_id, 'Vue.js'),
        (project2_id, 'Node.js'),
        (project2_id, 'Socket.io'),
        (project3_id, 'React'),
        (project3_id, 'Tailwind CSS'),
        (project3_id, 'Framer Motion');
    END IF;
END $$;
