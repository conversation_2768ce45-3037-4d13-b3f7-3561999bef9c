import Link from "next/link"
import { ArrowR<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type PersonalInfo } from "@/actions/portfolio-actions"

interface HeroSectionProps {
  personalInfo: PersonalInfo
}

export default function HeroSection({ personalInfo }: HeroSectionProps) {
  return (
    <section className="pt-32 pb-16 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div className="space-y-6">
          <h1 className="text-5xl md:text-6xl font-bold mb-4 animate-float">
            Hello, I'm <span className="gradient-text">{personalInfo.name}</span>
          </h1>
          <p className="text-lg text-gray-600 mb-8 animate-shimmer">{personalInfo.role}</p>
          <div className="flex gap-4">
            <Button asChild className="interactive-hover">
              <Link href="#projects">
                View Projects <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" asChild className="interactive-hover">
              <Link href="#contact">Get in Touch</Link>
            </Button>
          </div>
        </div>
        <div className="relative animate-float">
          <div className="aspect-square bg-gray-100 rounded-full overflow-hidden animate-pulse-glow">
            <img
              src={personalInfo.profileImage || "/placeholder.svg?height=500&width=500"}
              alt={`${personalInfo.name}'s profile`}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-rose-500 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
      </div>
    </section>
  )
}
