import SocialIcon from "@/components/ui/social-icon"
import { type SocialLink, type PersonalInfo } from "@/actions/portfolio-actions"

interface FooterProps {
  personalInfo: PersonalInfo
  socialLinks: SocialLink[]
}

export default function Footer({ personalInfo, socialLinks }: FooterProps) {
  return (
    <footer className="py-12 px-6 md:px-12 lg:px-24 border-t">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
        <p className="text-gray-600 mb-4 md:mb-0">
          © {new Date().getFullYear()} {personalInfo.name} • {personalInfo.location} •{" "}
          <a href={`https://${personalInfo.website}`} className="hover:underline">
            {personalInfo.website}
          </a>
        </p>
        <div className="flex gap-6">
          {socialLinks.map((link) => (
            <a
              key={link.platform}
              href={link.url}
              className="text-gray-600 hover:text-rose-500 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              <span className="sr-only">{link.platform}</span>
              <SocialIcon platform={link.platform} />
            </a>
          ))}
        </div>
      </div>
    </footer>
  )
}
