"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ap, 
  Star, 
  Heart, 
  <PERSON>, 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  Eye,
  Move,
  RotateCcw
} from "lucide-react"

export default function AnimationDemoPage() {
  const [clickCount, setClickCount] = useState(0)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold gradient-text animate-float">
          🎨 Animation Showcase
        </h1>
        <p className="text-lg text-gray-600 animate-shimmer">
          Experience beautiful cursor effects and background animations
        </p>
      </div>

      {/* Cursor Demo Section */}
      <Card className="card-hover glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MousePointer className="h-5 w-5 animate-pulse" />
            Custom Cursor Effects
          </CardTitle>
          <CardDescription>
            Move your mouse around to see the beautiful cursor animations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-6 border rounded-lg text-center space-y-2">
              <Eye className="h-8 w-8 mx-auto text-blue-500 animate-pulse" />
              <h3 className="font-semibold">Hover Detection</h3>
              <p className="text-sm text-gray-600">Cursor changes on interactive elements</p>
            </div>
            
            <div className="p-6 border rounded-lg text-center space-y-2">
              <Move className="h-8 w-8 mx-auto text-purple-500 animate-bounce" />
              <h3 className="font-semibold">Smooth Following</h3>
              <p className="text-sm text-gray-600">Trailing particles follow your cursor</p>
            </div>
            
            <div className="p-6 border rounded-lg text-center space-y-2">
              <Zap className="h-8 w-8 mx-auto text-pink-500 animate-pulse" />
              <h3 className="font-semibold">Click Effects</h3>
              <p className="text-sm text-gray-600">Beautiful ripple effects on click</p>
            </div>
          </div>

          <div className="text-center space-y-4">
            <Button 
              onClick={() => setClickCount(prev => prev + 1)}
              className="interactive-hover animate-pulse-glow"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              Click me! ({clickCount})
            </Button>
            <p className="text-sm text-gray-500">
              Try clicking buttons, hovering over cards, and moving your mouse around
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Animation Classes Demo */}
      <Card className="card-hover">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 animate-spin" />
            Animation Classes
          </CardTitle>
          <CardDescription>
            Various animation effects available throughout the app
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Float Animation */}
            <div className="p-4 border rounded-lg text-center space-y-3">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mx-auto animate-float"></div>
              <h3 className="font-semibold">Float Animation</h3>
              <Badge variant="outline">animate-float</Badge>
            </div>

            {/* Pulse Glow */}
            <div className="p-4 border rounded-lg text-center space-y-3">
              <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto animate-pulse-glow"></div>
              <h3 className="font-semibold">Pulse Glow</h3>
              <Badge variant="outline">animate-pulse-glow</Badge>
            </div>

            {/* Shimmer */}
            <div className="p-4 border rounded-lg text-center space-y-3">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-400 to-red-500 rounded-full mx-auto animate-shimmer"></div>
              <h3 className="font-semibold">Shimmer</h3>
              <Badge variant="outline">animate-shimmer</Badge>
            </div>

            {/* Gradient Text */}
            <div className="p-4 border rounded-lg text-center space-y-3">
              <h3 className="text-2xl font-bold gradient-text">Gradient</h3>
              <h3 className="font-semibold">Gradient Text</h3>
              <Badge variant="outline">gradient-text</Badge>
            </div>

            {/* Glass Effect */}
            <div className="p-4 border rounded-lg text-center space-y-3 glass">
              <div className="w-16 h-16 bg-white/20 rounded-full mx-auto flex items-center justify-center">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-semibold">Glass Morphism</h3>
              <Badge variant="outline">glass</Badge>
            </div>

            {/* Interactive Hover */}
            <div className="p-4 border rounded-lg text-center space-y-3 interactive-hover">
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mx-auto flex items-center justify-center">
                <Heart className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-semibold">Interactive Hover</h3>
              <Badge variant="outline">interactive-hover</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Background Animation Info */}
      <Card className="card-hover">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 animate-spin" />
            Background Animations
          </CardTitle>
          <CardDescription>
            Beautiful animated background elements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Rocket className="h-4 w-4" />
                Floating Particles
              </h3>
              <p className="text-sm text-gray-600">
                50+ animated particles floating across the screen with different colors and speeds
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <RotateCcw className="h-4 w-4 animate-spin" />
                Gradient Orbs
              </h3>
              <p className="text-sm text-gray-600">
                Large animated gradient orbs that slowly move and scale in the background
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <MousePointer className="h-4 w-4" />
                Mouse Interactive Glow
              </h3>
              <p className="text-sm text-gray-600">
                A subtle glow effect that follows your mouse cursor around the screen
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Sparkles className="h-4 w-4 animate-pulse" />
                Geometric Shapes
              </h3>
              <p className="text-sm text-gray-600">
                Moving geometric shapes that travel across the screen in different patterns
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Performance Optimized</h4>
            <p className="text-sm text-blue-700">
              All animations are GPU-accelerated and optimized for smooth performance. 
              Background animations are automatically disabled on mobile devices to preserve battery life.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Elements */}
      <Card className="card-hover">
        <CardHeader>
          <CardTitle className="gradient-text">Try These Interactive Elements</CardTitle>
          <CardDescription>
            Hover and click to see various animation effects
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="interactive-hover animate-float">
              <Sparkles className="mr-2 h-4 w-4" />
              Sparkle
            </Button>
            
            <Button variant="outline" className="interactive-hover">
              <Zap className="mr-2 h-4 w-4 animate-pulse" />
              Zap
            </Button>
            
            <Button variant="secondary" className="interactive-hover animate-pulse-glow">
              <Star className="mr-2 h-4 w-4 animate-spin" />
              Star
            </Button>
            
            <Button variant="destructive" className="interactive-hover">
              <Heart className="mr-2 h-4 w-4 animate-bounce" />
              Heart
            </Button>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 mb-4">
              Move your cursor around this page to experience the full animation system!
            </p>
            <Badge className="animate-shimmer">
              ✨ Beautiful animations everywhere ✨
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
