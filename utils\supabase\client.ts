import { createBrowserClient } from "@supabase/ssr"
import type { Database } from "@/types/supabase"
import { config } from "@/lib/config"

let supabaseClient: ReturnType<typeof createBrowserClient<Database>> | null = null

export function createClientSupabaseClient() {
  if (supabaseClient === null) {
    supabaseClient = createBrowserClient<Database>(
      config.supabase.publicUrl, 
      config.supabase.publicAnonKey
    )
  }

  return supabaseClient
}

