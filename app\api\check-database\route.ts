import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    const results: any = {
      success: true,
      tables: {},
      summary: {
        totalTables: 0,
        tablesWithData: 0,
        missingTables: [],
        issues: []
      }
    }

    // List of expected tables
    const expectedTables = [
      'personal_info',
      'technologies', 
      'projects',
      'project_tags',
      'social_links',
      'github_stats'
    ]

    // Check each table
    for (const tableName of expectedTables) {
      try {
        // Check if table exists and get row count
        const { count, error } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })

        if (error) {
          results.tables[tableName] = {
            exists: false,
            error: error.message,
            count: 0
          }
          results.summary.missingTables.push(tableName)
          results.summary.issues.push(`Table ${tableName} does not exist or is not accessible`)
        } else {
          results.tables[tableName] = {
            exists: true,
            count: count || 0,
            hasData: (count || 0) > 0
          }
          results.summary.totalTables++
          if ((count || 0) > 0) {
            results.summary.tablesWithData++
          }
        }
      } catch (tableError) {
        results.tables[tableName] = {
          exists: false,
          error: tableError instanceof Error ? tableError.message : 'Unknown error',
          count: 0
        }
        results.summary.missingTables.push(tableName)
        results.summary.issues.push(`Error checking table ${tableName}`)
      }
    }

    // Check for specific columns in personal_info
    if (results.tables.personal_info?.exists) {
      try {
        const { data: sampleData, error: sampleError } = await supabase
          .from('personal_info')
          .select('id, name, role, bio, location, website, email, profile_image')
          .limit(1)

        if (sampleError) {
          results.summary.issues.push(`personal_info table missing required columns: ${sampleError.message}`)
        } else {
          results.tables.personal_info.hasRequiredColumns = true
          results.tables.personal_info.sampleData = sampleData?.[0] || null
        }
      } catch (columnError) {
        results.summary.issues.push('Could not verify personal_info table structure')
      }
    }

    // Overall health check
    const isHealthy = results.summary.missingTables.length === 0 && 
                     results.summary.issues.length === 0 &&
                     results.summary.tablesWithData >= 3 // At least personal_info, technologies, and social_links should have data

    results.summary.isHealthy = isHealthy
    results.summary.needsMigration = !isHealthy

    return NextResponse.json(results)

  } catch (error) {
    console.error("Database check error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to check database status"
      },
      { status: 500 }
    )
  }
}
