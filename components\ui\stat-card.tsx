interface StatCardProps {
  title: string
  value: number | string
  description: string
}

export default function StatCard({ title, value, description }: StatCardProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-4xl font-bold text-rose-500">{value}</p>
      <p className="text-gray-500 mt-1">{description}</p>
    </div>
  )
}
