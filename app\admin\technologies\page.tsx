"use client"

import type React from "react"

import { useState } from "react"
import { usePortfolio } from "@/contexts/portfolio-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { X, Plus } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function TechnologiesPage() {
  const { data, updateTechnologies } = usePortfolio()
  const [technologies, setTechnologies] = useState<string[]>(data.technologies)
  const [newTech, setNewTech] = useState("")

  const handleAddTech = () => {
    if (!newTech.trim()) return

    // Check if technology already exists
    if (technologies.includes(newTech.trim())) {
      toast({
        title: "Technology already exists",
        description: `${newTech} is already in your tech stack.`,
        variant: "destructive",
      })
      return
    }

    setTechnologies((prev) => [...prev, newTech.trim()])
    setNewTech("")
  }

  const handleRemoveTech = (tech: string) => {
    setTechnologies((prev) => prev.filter((t) => t !== tech))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateTechnologies(technologies)
    toast({
      title: "Technologies updated",
      description: "Your tech stack has been updated successfully.",
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Technologies</h1>
        <p className="text-gray-500 mt-2">Manage your tech stack</p>
      </div>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Edit Technologies</CardTitle>
            <CardDescription>Add or remove technologies from your tech stack</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input placeholder="Add a technology..." value={newTech} onChange={(e) => setNewTech(e.target.value)} />
              <Button type="button" onClick={handleAddTech} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              {technologies.map((tech) => (
                <Badge key={tech} variant="secondary" className="flex items-center gap-1 px-3 py-1">
                  {tech}
                  <button
                    type="button"
                    onClick={() => handleRemoveTech(tech)}
                    className="ml-1 rounded-full hover:bg-gray-200 p-1"
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove {tech}</span>
                  </button>
                </Badge>
              ))}
              {technologies.length === 0 && <p className="text-sm text-gray-500">No technologies added yet.</p>}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit">Save Changes</Button>
          </CardFooter>
        </form>
      </Card>
      <Toaster />
    </div>
  )
}
