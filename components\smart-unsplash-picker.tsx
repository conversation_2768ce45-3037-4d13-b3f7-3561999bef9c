"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Lightbulb, <PERSON>rk<PERSON>, Zap } from "lucide-react"
import UnsplashImagePicker from "./unsplash-image-picker"

interface SmartUnsplashPickerProps {
  onImageSelected: (imageUrl: string, imageData?: any) => void
  currentImageUrl?: string
  projectTitle?: string
  projectDescription?: string
  projectTags?: string[]
  className?: string
}

export default function SmartUnsplashPicker({
  onImageSelected,
  currentImageUrl,
  projectTitle,
  projectDescription,
  projectTags = [],
  className
}: SmartUnsplashPickerProps) {
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [selectedSuggestion, setSelectedSuggestion] = useState<string>("")
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)

  // Generate smart suggestions based on project data
  useEffect(() => {
    generateSuggestions()
  }, [projectTitle, projectDescription, projectTags])

  const generateSuggestions = async () => {
    setIsLoadingSuggestions(true)
    
    try {
      // Extract keywords from project data
      const keywords = extractKeywords()
      
      // Get AI-powered suggestions
      const response = await fetch("/api/unsplash/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          projectType: detectProjectType(),
          technology: detectTechnology(),
          keywords
        })
      })

      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.suggestions || [])
        setSelectedSuggestion(data.recommended || keywords[0] || "technology")
      } else {
        // Fallback suggestions
        setSuggestions(keywords.length > 0 ? keywords : ["technology", "web development", "modern design"])
        setSelectedSuggestion(keywords[0] || "technology")
      }
    } catch (error) {
      console.error("Failed to generate suggestions:", error)
      // Fallback suggestions
      const keywords = extractKeywords()
      setSuggestions(keywords.length > 0 ? keywords : ["technology", "web development", "modern design"])
      setSelectedSuggestion(keywords[0] || "technology")
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const extractKeywords = (): string[] => {
    const keywords = new Set<string>()
    
    // Add tags
    projectTags.forEach(tag => {
      keywords.add(tag.toLowerCase())
    })
    
    // Extract from title
    if (projectTitle) {
      const titleWords = projectTitle.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 3)
      titleWords.forEach(word => keywords.add(word))
    }
    
    // Extract from description
    if (projectDescription) {
      const descWords = projectDescription.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 4)
        .slice(0, 5) // Limit to first 5 meaningful words
      descWords.forEach(word => keywords.add(word))
    }
    
    return Array.from(keywords).slice(0, 8) // Limit to 8 keywords
  }

  const detectProjectType = (): string => {
    const text = `${projectTitle} ${projectDescription} ${projectTags.join(" ")}`.toLowerCase()
    
    if (text.includes("mobile") || text.includes("app") || text.includes("ios") || text.includes("android")) {
      return "mobile"
    }
    if (text.includes("web") || text.includes("website") || text.includes("frontend") || text.includes("backend")) {
      return "web"
    }
    if (text.includes("api") || text.includes("server") || text.includes("database")) {
      return "api"
    }
    if (text.includes("ai") || text.includes("machine learning") || text.includes("ml")) {
      return "ai"
    }
    if (text.includes("game") || text.includes("gaming")) {
      return "game"
    }
    if (text.includes("blockchain") || text.includes("crypto")) {
      return "blockchain"
    }
    if (text.includes("ecommerce") || text.includes("shop") || text.includes("store")) {
      return "ecommerce"
    }
    
    return "default"
  }

  const detectTechnology = (): string => {
    const text = `${projectTitle} ${projectDescription} ${projectTags.join(" ")}`.toLowerCase()
    
    if (text.includes("react")) return "react"
    if (text.includes("vue")) return "vue"
    if (text.includes("angular")) return "angular"
    if (text.includes("node")) return "node"
    if (text.includes("python")) return "python"
    if (text.includes("java")) return "java"
    if (text.includes("c#") || text.includes("csharp")) return "csharp"
    if (text.includes("php")) return "php"
    
    return "default"
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSelectedSuggestion(suggestion)
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Smart Suggestions */}
        {suggestions.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Sparkles className="h-4 w-4 text-purple-500" />
                Smart Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-2">
                {suggestions.map((suggestion, index) => (
                  <Badge
                    key={suggestion}
                    variant={selectedSuggestion === suggestion ? "default" : "outline"}
                    className={`cursor-pointer transition-all ${
                      selectedSuggestion === suggestion 
                        ? "bg-blue-500 text-white" 
                        : "hover:bg-blue-50"
                    }`}
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    {index === 0 && <Zap className="h-3 w-3 mr-1" />}
                    {suggestion}
                  </Badge>
                ))}
              </div>
              {selectedSuggestion && (
                <p className="text-xs text-gray-500 mt-2">
                  <Lightbulb className="h-3 w-3 inline mr-1" />
                  Searching for: <span className="font-medium">{selectedSuggestion}</span>
                </p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Unsplash Image Picker */}
        <UnsplashImagePicker
          onImageSelected={onImageSelected}
          currentImageUrl={currentImageUrl}
          searchPlaceholder="Search for project images..."
          defaultQuery={selectedSuggestion || "technology"}
          key={selectedSuggestion} // Force re-render when suggestion changes
        />
      </div>
    </div>
  )
}
