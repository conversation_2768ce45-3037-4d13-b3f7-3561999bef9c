import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    const tableStatus = {}
    
    // List of tables to check
    const tables = [
      'personal_info',
      'technologies', 
      'projects',
      'project_tags',
      'social_links',
      'github_stats'
    ]

    for (const tableName of tables) {
      try {
        // Try to query the table to see if it exists
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })

        if (error) {
          tableStatus[tableName] = {
            exists: false,
            error: error.message,
            needsCreation: true
          }
        } else {
          tableStatus[tableName] = {
            exists: true,
            count: count || 0,
            hasData: (count || 0) > 0
          }
        }
      } catch (err) {
        tableStatus[tableName] = {
          exists: false,
          error: err instanceof Error ? err.message : 'Unknown error',
          needsCreation: true
        }
      }
    }

    return NextResponse.json({
      success: true,
      tables: tableStatus,
      summary: {
        totalTables: tables.length,
        existingTables: Object.values(tableStatus).filter((t: any) => t.exists).length,
        missingTables: Object.entries(tableStatus)
          .filter(([_, status]: [string, any]) => !status.exists)
          .map(([name, _]) => name)
      }
    })

  } catch (error) {
    console.error("Check tables error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to check tables"
      },
      { status: 500 }
    )
  }
}
