import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    // Lấy thông tin về bảng personal_info
    const { data: tableInfo, error: tableError } = await supabase
      .rpc("get_table_info", { table_name: "personal_info" })
      .select("*")

    if (tableError) {
      // Nếu không thể sử dụng RPC, thử cách khác
      const { data: personalInfo, error: fetchError } = await supabase.from("personal_info").select("*").limit(1)

      if (fetchError) {
        return NextResponse.json(
          {
            success: false,
            error: fetchError.message,
            message: "Không thể lấy thông tin bảng",
          },
          { status: 500 },
        )
      }

      return NextResponse.json({
        success: true,
        tableStructure: "Không thể lấy cấu trúc bảng",
        currentData: personalInfo,
      })
    }

    // L<PERSON>y dữ liệu hiện tại
    const { data: personalInfo, error: fetchError } = await supabase.from("personal_info").select("*").limit(1)

    if (fetchError) {
      return NextResponse.json(
        {
          success: false,
          error: fetchError.message,
          message: "Không thể lấy dữ liệu hiện tại",
        },
        { status: 500 },
      )
    }

    return NextResponse.json({
      success: true,
      tableStructure: tableInfo,
      currentData: personalInfo,
    })
  } catch (error) {
    console.error("Lỗi khi debug database:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Đã xảy ra lỗi không mong muốn",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
