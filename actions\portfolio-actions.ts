"use server"

import { createServerSupabaseClient } from "@/utils/supabase/server"
import { revalidatePath } from "next/cache"
import { generateFallbackData } from "@/utils/fallback-data"

export type PersonalInfo = {
  name: string
  role: string
  bio: string
  location: string
  website: string
  email: string
  profileImage?: string
}

export type Project = {
  id: string
  title: string
  description: string
  tags: string[]
  imageUrl: string
}

export type SocialLink = {
  id?: string
  platform: string
  url: string
}

export type GithubStats = {
  stars: number
  repos: number
  followers: number
}

// Fetch all portfolio data
export async function fetchPortfolioData() {
  try {
    const supabase = await createServerSupabaseClient()

    // Fetch personal info
    let personalInfo = null
    try {
      const { data: personalInfoData, error: personalInfoError } = await supabase
        .from("personal_info")
        .select("*")
        .limit(1)

      if (personalInfoError) {
        console.error("Error fetching personal info:", personalInfoError)
      } else if (personalInfoData && personalInfoData.length > 0) {
        personalInfo = personalInfoData[0]
      }
    } catch (error) {
      console.error("Exception fetching personal info:", error)
    }

    // Fetch technologies
    const { data: technologies, error: technologiesError } = await supabase
      .from("technologies")
      .select("name")
      .order("name")

    // If there's an error fetching technologies, use fallback data
    if (technologiesError) {
      console.error("Error fetching technologies:", technologiesError)
      return generateFallbackData()
    }

    // Fetch projects with their tags
    const { data: projects, error: projectsError } = await supabase.from("projects").select("*").order("created_at")

    // If there's an error fetching projects, use fallback data
    if (projectsError) {
      console.error("Error fetching projects:", projectsError)
      return generateFallbackData()
    }

    const projectsWithTags = await Promise.all(
      (projects || []).map(async (project) => {
        const { data: tags } = await supabase.from("project_tags").select("tag").eq("project_id", project.id)

        return {
          id: project.id,
          title: project.title,
          description: project.description,
          imageUrl: project.image_url,
          tags: tags?.map((t) => t.tag) || [],
        }
      }),
    )

    // Fetch social links
    const { data: socialLinks, error: socialLinksError } = await supabase
      .from("social_links")
      .select("*")
      .order("platform")

    // If there's an error fetching social links, use fallback data
    if (socialLinksError) {
      console.error("Error fetching social links:", socialLinksError)
      return generateFallbackData()
    }

    // Fetch GitHub stats
    let githubStats = null
    try {
      const { data: githubStatsData, error: githubStatsError } = await supabase
        .from("github_stats")
        .select("*")
        .limit(1)

      if (githubStatsError) {
        console.error("Error fetching GitHub stats:", githubStatsError)
      } else if (githubStatsData && githubStatsData.length > 0) {
        githubStats = githubStatsData[0]
      }
    } catch (error) {
      console.error("Exception fetching GitHub stats:", error)
    }

    return {
      personalInfo: personalInfo
        ? {
            name: personalInfo.name,
            role: personalInfo.role,
            bio: personalInfo.bio,
            location: personalInfo.location || "",
            website: personalInfo.website || "",
            email: personalInfo.email,
            profileImage: personalInfo.profile_image || "", // Add this line
          }
        : generateFallbackData().personalInfo,
      technologies: technologies?.map((t) => t.name) || generateFallbackData().technologies,
      projects: projectsWithTags.length > 0 ? projectsWithTags : generateFallbackData().projects,
      socialLinks:
        socialLinks?.map((link) => ({
          id: link.id,
          platform: link.platform,
          url: link.url,
        })) || generateFallbackData().socialLinks,
      githubStats: githubStats
        ? {
            stars: githubStats.stars,
            repos: githubStats.repos,
            followers: githubStats.followers,
          }
        : generateFallbackData().githubStats,
    }
  } catch (error) {
    console.error("Error fetching portfolio data:", error)
    // Return fallback data if any error occurs
    return generateFallbackData()
  }
}

// Update personal info with fallback handling
export async function updatePersonalInfo(info: PersonalInfo) {
  try {
    const supabase = await createServerSupabaseClient()

    // const { data: existingInfo, error: fetchError } = await supabase.from("personal_info").select("id").single()
    const { data: existingInfoData, error: fetchError } = await supabase.from("personal_info").select("id").limit(1)

    const existingInfo = existingInfoData && existingInfoData.length > 0 ? existingInfoData[0] : null

    if (fetchError && fetchError.code !== "PGRST116") {
      console.error("Error fetching personal info:", fetchError)
      return { success: false, error: "Database error", usingFallback: true }
    }

    if (existingInfo) {
      console.log("Updating personal info with:", {
        name: info.name,
        role: info.role,
        bio: info.bio,
        location: info.location,
        website: info.website,
        email: info.email,
        profile_image: info.profileImage,
      })

      const { error } = await supabase
        .from("personal_info")
        .update({
          name: info.name,
          role: info.role,
          bio: info.bio,
          location: info.location,
          website: info.website,
          email: info.email,
          profile_image: info.profileImage,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingInfo.id)

      if (error) {
        console.error("Error updating personal info:", error)
        return { success: false, error: "Database error", usingFallback: true }
      }

      // Add revalidation for all paths that might display personal info
      revalidatePath("/")
      revalidatePath("/admin/personal-info")
      revalidatePath("/admin/dashboard")
    } else {
      const { error } = await supabase.from("personal_info").insert({
        name: info.name,
        role: info.role,
        bio: info.bio,
        location: info.location,
        website: info.website,
        email: info.email,
        profile_image: info.profileImage, // Add this line
      })

      if (error) {
        console.error("Error inserting personal info:", error)
        return { success: false, error: "Database error", usingFallback: true }
      }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updatePersonalInfo:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}

// Update technologies with fallback handling
export async function updateTechnologies(techs: string[]) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get existing technologies
    const { data: existingTechs, error: fetchError } = await supabase.from("technologies").select("name")

    if (fetchError) {
      console.error("Error fetching technologies:", fetchError)
      return { success: false, error: "Database error", usingFallback: true }
    }

    const existingTechNames = existingTechs?.map((t) => t.name) || []

    // Technologies to add
    const techsToAdd = techs.filter((t) => !existingTechNames.includes(t))

    // Technologies to remove
    const techsToRemove = existingTechNames.filter((t) => !techs.includes(t))

    // Add new technologies
    if (techsToAdd.length > 0) {
      const { error } = await supabase.from("technologies").insert(techsToAdd.map((name) => ({ name })))
      if (error) {
        console.error("Error adding technologies:", error)
        return { success: false, error: "Database error", usingFallback: true }
      }
    }

    // Remove technologies that are no longer needed
    if (techsToRemove.length > 0) {
      for (const tech of techsToRemove) {
        const { error } = await supabase.from("technologies").delete().eq("name", tech)
        if (error) {
          console.error(`Error removing technology ${tech}:`, error)
          // Continue with other deletions even if one fails
        }
      }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateTechnologies:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}

// Add a new project with fallback handling
export async function addProject(project: Omit<Project, "id">) {
  try {
    const supabase = await createServerSupabaseClient()

    // Insert project
    const { data: newProject, error } = await supabase
      .from("projects")
      .insert({
        title: project.title,
        description: project.description,
        image_url: project.imageUrl,
      })
      .select("id")
      .single()

    if (error || !newProject) {
      console.error("Error creating project:", error)
      return {
        success: false,
        error: error?.message || "Failed to create project",
        usingFallback: true,
        id: `fallback-${Date.now()}`,
      }
    }

    // Insert tags
    if (project.tags.length > 0) {
      const { error: tagsError } = await supabase.from("project_tags").insert(
        project.tags.map((tag) => ({
          project_id: newProject.id,
          tag,
        })),
      )

      if (tagsError) {
        console.error("Error adding project tags:", tagsError)
        // Continue even if tags fail
      }
    }

    revalidatePath("/")
    return { success: true, id: newProject.id }
  } catch (error) {
    console.error("Error in addProject:", error)
    return {
      success: false,
      error: "Server error",
      usingFallback: true,
      id: `fallback-${Date.now()}`,
    }
  }
}

// Update an existing project with fallback handling
export async function updateProject(project: Project) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check if project exists
    const { data: existingProject, error: checkError } = await supabase
      .from("projects")
      .select("id")
      .eq("id", project.id)
      .single()

    if (checkError || !existingProject) {
      console.error("Error checking project existence:", checkError)
      return { success: false, error: "Project not found", usingFallback: true }
    }

    // Update project
    const { error } = await supabase
      .from("projects")
      .update({
        title: project.title,
        description: project.description,
        image_url: project.imageUrl,
        updated_at: new Date().toISOString(),
      })
      .eq("id", project.id)

    if (error) {
      console.error("Error updating project:", error)
      return { success: false, error: "Database error", usingFallback: true }
    }

    // Delete existing tags
    const { error: deleteTagsError } = await supabase.from("project_tags").delete().eq("project_id", project.id)

    if (deleteTagsError) {
      console.error("Error deleting project tags:", deleteTagsError)
      // Continue even if tag deletion fails
    }

    // Insert new tags
    if (project.tags.length > 0) {
      const { error: insertTagsError } = await supabase.from("project_tags").insert(
        project.tags.map((tag) => ({
          project_id: project.id,
          tag,
        })),
      )

      if (insertTagsError) {
        console.error("Error inserting project tags:", insertTagsError)
        // Continue even if tag insertion fails
      }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateProject:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}

// Delete a project with fallback handling
export async function deleteProject(id: string) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check if project exists
    const { data: existingProject, error: checkError } = await supabase
      .from("projects")
      .select("id")
      .eq("id", id)
      .single()

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Error checking project existence:", checkError)
      return { success: false, error: "Database error", usingFallback: true }
    }

    if (!existingProject) {
      // If project doesn't exist in DB but has a fallback ID, just return success
      if (id.startsWith("fallback-")) {
        return { success: true }
      }
      return { success: false, error: "Project not found", usingFallback: true }
    }

    // Delete project (tags will be deleted automatically due to CASCADE)
    const { error } = await supabase.from("projects").delete().eq("id", id)

    if (error) {
      console.error("Error deleting project:", error)
      return { success: false, error: "Database error", usingFallback: true }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in deleteProject:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}

// Update social links with fallback handling
export async function updateSocialLinks(links: SocialLink[]) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get existing links
    const { data: existingLinks, error: fetchError } = await supabase.from("social_links").select("id, platform")

    if (fetchError) {
      console.error("Error fetching social links:", fetchError)
      return { success: false, error: "Database error", usingFallback: true }
    }

    // Create a map of platform to id for existing links
    const existingLinksMap = new Map((existingLinks || []).map((link) => [link.platform, link.id]))

    // Process each link
    for (const link of links) {
      const existingId = existingLinksMap.get(link.platform)

      if (existingId) {
        // Update existing link
        const { error } = await supabase
          .from("social_links")
          .update({
            url: link.url,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingId)

        if (error) {
          console.error(`Error updating social link for ${link.platform}:`, error)
          // Continue with other links even if one fails
        }

        // Remove from map to track which ones to delete
        existingLinksMap.delete(link.platform)
      } else {
        // Insert new link
        const { error } = await supabase.from("social_links").insert({
          platform: link.platform,
          url: link.url,
        })

        if (error) {
          console.error(`Error inserting social link for ${link.platform}:`, error)
          // Continue with other links even if one fails
        }
      }
    }

    // Delete links that are no longer needed
    for (const id of existingLinksMap.values()) {
      const { error } = await supabase.from("social_links").delete().eq("id", id)
      if (error) {
        console.error(`Error deleting social link with id ${id}:`, error)
        // Continue with other deletions even if one fails
      }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateSocialLinks:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}

// Update GitHub stats with fallback handling
export async function updateGithubStats(stats: GithubStats) {
  try {
    const supabase = await createServerSupabaseClient()

    const { data: existingStatsData, error: fetchError } = await supabase.from("github_stats").select("id").limit(1)

    const existingStats = existingStatsData && existingStatsData.length > 0 ? existingStatsData[0] : null

    if (fetchError && fetchError.code !== "PGRST116") {
      console.error("Error fetching GitHub stats:", fetchError)
      return { success: false, error: "Database error", usingFallback: true }
    }

    if (existingStats) {
      const { error } = await supabase
        .from("github_stats")
        .update({
          stars: stats.stars,
          repos: stats.repos,
          followers: stats.followers,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingStats.id)

      if (error) {
        console.error("Error updating GitHub stats:", error)
        return { success: false, error: "Database error", usingFallback: true }
      }
    } else {
      const { error } = await supabase.from("github_stats").insert({
        stars: stats.stars,
        repos: stats.repos,
        followers: stats.followers,
      })

      if (error) {
        console.error("Error inserting GitHub stats:", error)
        return { success: false, error: "Database error", usingFallback: true }
      }
    }

    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateGithubStats:", error)
    return { success: false, error: "Server error", usingFallback: true }
  }
}
