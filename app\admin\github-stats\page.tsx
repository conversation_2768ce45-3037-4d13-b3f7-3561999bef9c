"use client"

import type React from "react"

import { useState } from "react"
import { usePortfolio, type GithubStats } from "@/contexts/portfolio-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function GithubStatsPage() {
  const { data, updateGithubStats } = usePortfolio()
  const [formData, setFormData] = useState<GithubStats>(data.githubStats)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numValue = Number.parseInt(value) || 0
    setFormData((prev) => ({ ...prev, [name]: numValue }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateGithubStats(formData)
    toast({
      title: "GitHub stats updated",
      description: "Your GitHub statistics have been updated successfully.",
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">GitHub Stats</h1>
        <p className="text-gray-500 mt-2">Update your GitHub statistics</p>
      </div>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Edit GitHub Stats</CardTitle>
            <CardDescription>Update your GitHub statistics to display on your portfolio</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="stars">Total Stars</Label>
              <Input
                id="stars"
                name="stars"
                type="number"
                min="0"
                value={formData.stars}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="repos">Repositories</Label>
              <Input
                id="repos"
                name="repos"
                type="number"
                min="0"
                value={formData.repos}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="followers">Followers</Label>
              <Input
                id="followers"
                name="followers"
                type="number"
                min="0"
                value={formData.followers}
                onChange={handleChange}
                required
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit">Save Changes</Button>
          </CardFooter>
        </form>
      </Card>
      <Toaster />
    </div>
  )
}
