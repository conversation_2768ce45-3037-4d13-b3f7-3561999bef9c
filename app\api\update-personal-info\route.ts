import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { revalidatePath } from "next/cache"

export async function POST(request: Request) {
  try {
    const supabase = createServiceSupabaseClient()
    const data = await request.json()

    console.log("API route received personal info update:", data)

    // Check if personal_info table has any records
    const { data: existingData, error: fetchError } = await supabase.from("personal_info").select("id").limit(1)

    if (fetchError) {
      console.error("Error fetching personal info:", fetchError)
      return NextResponse.json({ success: false, error: fetchError.message }, { status: 500 })
    }

    let result

    if (existingData && existingData.length > 0) {
      // Update existing record
      const { error } = await supabase
        .from("personal_info")
        .update({
          name: data.name,
          role: data.role,
          bio: data.bio,
          location: data.location,
          website: data.website,
          email: data.email,
          profile_image: data.profileImage,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingData[0].id)

      if (error) {
        console.error("Error updating personal info:", error)
        return NextResponse.json({ success: false, error: error.message }, { status: 500 })
      }

      result = { success: true, message: "Personal info updated" }
    } else {
      // Insert new record
      const { error } = await supabase.from("personal_info").insert({
        name: data.name,
        role: data.role,
        bio: data.bio,
        location: data.location,
        website: data.website,
        email: data.email,
        profile_image: data.profileImage,
      })

      if (error) {
        console.error("Error inserting personal info:", error)
        return NextResponse.json({ success: false, error: error.message }, { status: 500 })
      }

      result = { success: true, message: "Personal info created" }
    }

    // Revalidate all paths that might display personal info
    revalidatePath("/")
    revalidatePath("/admin/personal-info")
    revalidatePath("/admin/dashboard")

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in update-personal-info route:", error)
    return NextResponse.json(
      {
        success: false,
        message: "An unexpected error occurred",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
