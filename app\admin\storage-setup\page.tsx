"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Check, AlertCircle, Database, RefreshCw, ExternalLink, HardDrive } from "lucide-react"

export default function StorageSetupPage() {
  const [isRunningSetup, setIsRunningSetup] = useState(false)
  const [setupResult, setSetupResult] = useState<{
    success: boolean
    message: string
    error?: string
    results?: any[]
    needsManualSetup?: boolean
    needsPolicySetup?: boolean
    manualInstructions?: string[]
  } | null>(null)

  const runStorageSetup = async () => {
    setIsRunningSetup(true)
    setSetupResult(null)

    try {
      const response = await fetch("/api/setup-storage", {
        method: "POST",
      })

      const result = await response.json()
      setSetupResult(result)

    } catch (error) {
      console.error("Storage setup error:", error)
      setSetupResult({
        success: false,
        message: "Failed to run storage setup",
        error: error instanceof Error ? error.message : "Unknown error"
      })
    } finally {
      setIsRunningSetup(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Storage Setup</h1>
        <p className="text-gray-500 mt-2">
          Configure Supabase Storage for file uploads
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Configuration
          </CardTitle>
          <CardDescription>
            This will check and configure your Supabase Storage for the portfolio application.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600">
            <p className="mb-2">This setup will:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Check if you have access to Supabase Storage</li>
              <li>Create 'general' bucket if it doesn't exist</li>
              <li>Configure bucket as public</li>
              <li>Test upload permissions</li>
              <li>Provide manual setup instructions if needed</li>
            </ul>
          </div>

          {setupResult && (
            <Alert className={setupResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
              {setupResult.success ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={setupResult.success ? "text-green-800" : "text-red-800"}>
                {setupResult.message}
                {setupResult.error && (
                  <div className="mt-2 text-sm">
                    <strong>Error:</strong> {setupResult.error}
                  </div>
                )}
                {(setupResult.needsManualSetup || setupResult.needsPolicySetup) && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-yellow-800 font-medium">Manual setup required:</p>
                    {setupResult.manualInstructions && (
                      <ol className="text-yellow-700 text-sm mt-1 list-decimal list-inside space-y-1">
                        {setupResult.manualInstructions.map((instruction, index) => (
                          <li key={index}>{instruction}</li>
                        ))}
                      </ol>
                    )}
                  </div>
                )}
                {setupResult.results && (
                  <div className="mt-3">
                    <details className="text-sm">
                      <summary className="cursor-pointer font-medium">View detailed results</summary>
                      <div className="mt-2 space-y-1">
                        {setupResult.results.map((result: any, index: number) => (
                          <div key={index} className={`p-2 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}`}>
                            <span className="font-medium">{result.step}:</span> {result.message || result.error}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          <Button 
            onClick={runStorageSetup} 
            disabled={isRunningSetup}
            className="w-full"
          >
            {isRunningSetup ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Setting up Storage...
              </>
            ) : (
              <>
                <HardDrive className="mr-2 h-4 w-4" />
                Setup Storage
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Manual Storage Setup</CardTitle>
          <CardDescription>
            If automatic setup fails, follow these manual steps
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Step 1: Create Storage Bucket</h4>
              <ol className="list-decimal list-inside space-y-1 ml-4 text-gray-600">
                <li>Go to Supabase Dashboard → Storage</li>
                <li>Click "New bucket"</li>
                <li>Name: <code>general</code></li>
                <li>Make it public: ✅</li>
                <li>File size limit: 10MB</li>
                <li>Click "Save"</li>
              </ol>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Step 2: Configure Storage Policies</h4>
              <ol className="list-decimal list-inside space-y-1 ml-4 text-gray-600">
                <li>Go to Storage → Policies</li>
                <li>Click "New policy" for the 'general' bucket</li>
                <li>Create policies for: SELECT, INSERT, UPDATE, DELETE</li>
                <li>Allow public access or authenticated users</li>
                <li>Save all policies</li>
              </ol>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Step 3: Test Upload</h4>
              <p className="text-gray-600 ml-4">
                After setup, go to <a href="/admin/test-upload" className="text-blue-600 underline">Test Upload</a> to verify everything works.
              </p>
            </div>

            <div className="flex gap-2 mt-4">
              <Button asChild variant="outline">
                <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Open Supabase Dashboard
                </a>
              </Button>
              <Button asChild variant="outline">
                <a href="/admin/test-upload">
                  Test Upload
                </a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Common Storage Issues</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-3">
            <div>
              <h4 className="font-semibold text-red-600">Error: "new row violates row-level security policy"</h4>
              <p className="text-gray-600 ml-4">
                This means RLS (Row Level Security) is blocking storage operations. 
                You need to create storage policies or disable RLS for storage.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-red-600">Error: "Bucket already exists"</h4>
              <p className="text-gray-600 ml-4">
                This is actually good! The bucket exists, you just need to configure policies.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-red-600">Error: "Permission denied"</h4>
              <p className="text-gray-600 ml-4">
                Check your Supabase service role key and make sure it has storage permissions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
