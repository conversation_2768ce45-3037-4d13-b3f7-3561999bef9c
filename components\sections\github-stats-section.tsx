import { Github } from "lucide-react"
import StatCard from "@/components/ui/stat-card"
import { type GithubStats } from "@/actions/portfolio-actions"

interface GithubStatsSectionProps {
  githubStats: GithubStats
}

export default function GithubStatsSection({ githubStats }: GithubStatsSectionProps) {
  return (
    <section className="py-16 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto bg-gray-50">
      <h2 className="text-3xl font-bold mb-8 flex items-center">
        <Github className="mr-2 h-6 w-6" /> GitHub Stats
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard
          title="Stars"
          value={githubStats.stars}
          description="Total stars earned"
        />
        <StatCard
          title="Repositories"
          value={githubStats.repos}
          description="Public repositories"
        />
        <StatCard
          title="Followers"
          value={githubStats.followers}
          description="GitHub followers"
        />
      </div>
    </section>
  )
}
