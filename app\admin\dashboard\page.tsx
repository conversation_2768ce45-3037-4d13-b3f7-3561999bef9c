"use client"

import Link from "next/link"
import { usePort<PERSON>lio } from "@/contexts/portfolio-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { User, Code, Github, Share2, Layers, ArrowRight, AlertTriangle, RefreshCcw, Database, Settings } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useState, useEffect } from "react"

export default function DashboardPage() {
  const { data, usingFallback, refreshData } = usePortfolio()
  const [databaseStatus, setDatabaseStatus] = useState<any>(null)

  useEffect(() => {
    checkDatabaseStatus()
  }, [])

  const checkDatabaseStatus = async () => {
    try {
      const response = await fetch("/api/check-database")
      const status = await response.json()
      setDatabaseStatus(status)
    } catch (error) {
      console.error("Failed to check database status:", error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-gray-500 mt-2">Manage your portfolio content</p>
        </div>
        <Button onClick={() => refreshData()} variant="outline" size="sm">
          <RefreshCcw className="mr-2 h-4 w-4" /> Refresh Data
        </Button>
      </div>

      {usingFallback && (
        <Alert variant="warning" className="bg-yellow-50 border-yellow-200">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Unable to connect to the database. Using fallback data. Your changes will be saved locally but not persisted
            to the database.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" /> Personal Info
            </CardTitle>
            <CardDescription>Update your personal information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Name:</span>
                <span className="text-sm">{data.personalInfo.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Location:</span>
                <span className="text-sm">{data.personalInfo.location}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Website:</span>
                <span className="text-sm">{data.personalInfo.website}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm">
              <Link href="/admin/personal-info">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Layers className="mr-2 h-5 w-5" /> Technologies
            </CardTitle>
            <CardDescription>Manage your tech stack</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Total:</span>
                <span className="text-sm">{data.technologies.length} technologies</span>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {data.technologies.slice(0, 5).map((tech) => (
                  <span key={tech} className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                    {tech}
                  </span>
                ))}
                {data.technologies.length > 5 && (
                  <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                    +{data.technologies.length - 5} more
                  </span>
                )}
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm">
              <Link href="/admin/technologies">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Code className="mr-2 h-5 w-5" /> Projects
            </CardTitle>
            <CardDescription>Manage your portfolio projects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Total:</span>
                <span className="text-sm">{data.projects.length} projects</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.projects.slice(0, 3).map((project) => (
                  <li key={project.id} className="text-sm truncate">
                    • {project.title}
                  </li>
                ))}
                {data.projects.length > 3 && (
                  <li className="text-sm text-gray-500">+{data.projects.length - 3} more projects</li>
                )}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm">
              <Link href="/admin/projects">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Share2 className="mr-2 h-5 w-5" /> Social Links
            </CardTitle>
            <CardDescription>Manage your social media links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Total:</span>
                <span className="text-sm">{data.socialLinks.length} links</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.socialLinks.map((link) => (
                  <li key={link.platform} className="text-sm truncate">
                    • {link.platform}
                  </li>
                ))}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm">
              <Link href="/admin/social-links">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Github className="mr-2 h-5 w-5" /> GitHub Stats
            </CardTitle>
            <CardDescription>Update your GitHub statistics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Stars:</span>
                <span className="text-sm">{data.githubStats.stars}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Repositories:</span>
                <span className="text-sm">{data.githubStats.repos}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Followers:</span>
                <span className="text-sm">{data.githubStats.followers}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm">
              <Link href="/admin/github-stats">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Database className="mr-2 h-5 w-5" /> Database Status
            </CardTitle>
            <CardDescription>Check database health and run migrations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {databaseStatus ? (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Status:</span>
                    <span className={`text-sm ${databaseStatus.summary?.isHealthy ? 'text-green-600' : 'text-red-600'}`}>
                      {databaseStatus.summary?.isHealthy ? 'Healthy' : 'Needs Attention'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Tables:</span>
                    <span className="text-sm">{databaseStatus.summary?.totalTables || 0}/6</span>
                  </div>
                  {databaseStatus.summary?.issues?.length > 0 && (
                    <div className="text-xs text-red-600 mt-2">
                      {databaseStatus.summary.issues.length} issue(s) found
                    </div>
                  )}
                </>
              ) : (
                <div className="text-sm text-gray-500">Checking database status...</div>
              )}
            </div>
            <div className="flex gap-2 mt-4">
              <Button asChild className="flex-1" variant="outline" size="sm">
                <Link href="/admin/database-migration">
                  <Settings className="mr-2 h-4 w-4" />
                  Migrate
                </Link>
              </Button>
              <Button onClick={checkDatabaseStatus} variant="outline" size="sm">
                <RefreshCcw className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Preview Site</CardTitle>
            <CardDescription>View your portfolio website</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">See how your portfolio looks with the current content</p>
            <Button asChild className="w-full">
              <Link href="/" target="_blank">
                View Portfolio
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
