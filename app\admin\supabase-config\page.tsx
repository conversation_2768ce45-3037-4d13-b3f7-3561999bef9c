"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Loader2,
  Check,
  AlertTriangle,
  RefreshCw,
  Play,
  Database,
  Table,
  Code,
  Copy,
  ExternalLink,
  Plus,
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function SupabaseConfigPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(false)
  const [isInitializingTables, setIsInitializingTables] = useState(false)
  const [isCreatingBucket, setIsCreatingBucket] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<any>(null)
  const [initializeStatus, setInitializeStatus] = useState<any>(null)
  const [tablesStatus, setTablesStatus] = useState<any>(null)
  const [bucketStatus, setBucketStatus] = useState<any>(null)
  const [sqlScript, setSqlScript] = useState<string>("")
  const [bucketName, setBucketName] = useState<string>("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  // Kiểm tra kết nối khi trang được tải
  useEffect(() => {
    checkConnection()
    fetchSqlScript()
  }, [])

  // Kiểm tra kết nối đến Supabase
  const checkConnection = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/update-supabase-config")
      const data = await response.json()

      setConnectionStatus(data)

      if (data.success) {
        setSuccess("Kết nối thành công đến Supabase!")
      } else {
        setError(`Không thể kết nối đến Supabase: ${data.error || "Lỗi không xác định"}`)
      }
    } catch (err) {
      console.error("Lỗi khi kiểm tra kết nối:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsLoading(false)
    }
  }

  // Lấy SQL script
  const fetchSqlScript = async () => {
    try {
      const response = await fetch("/api/create-tables-sql")
      const data = await response.json()
      if (data.success && data.sql) {
        setSqlScript(data.sql)
      }
    } catch (err) {
      console.error("Lỗi khi lấy SQL script:", err)
    }
  }

  // Sao chép SQL script
  const copySqlScript = () => {
    navigator.clipboard.writeText(sqlScript)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // Khởi tạo buckets
  const initializeSupabase = async () => {
    setIsInitializing(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/initialize-supabase", {
        method: "POST",
      })
      const data = await response.json()

      setInitializeStatus(data)

      if (data.success) {
        setSuccess("Đã khởi tạo thành công buckets Supabase!")
      } else {
        setError(`Không thể khởi tạo buckets Supabase: ${data.error || "Lỗi không xác định"}`)
      }
    } catch (err) {
      console.error("Lỗi khi khởi tạo Supabase:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsInitializing(false)
    }
  }

  // Tạo bucket đơn lẻ
  const createSingleBucket = async () => {
    if (!bucketName.trim()) {
      setError("Vui lòng nhập tên bucket")
      return
    }

    setIsCreatingBucket(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/create-bucket-simple", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName: bucketName.trim() }),
      })
      const data = await response.json()

      setBucketStatus(data)

      if (data.success) {
        setSuccess(`Đã tạo/kiểm tra bucket "${bucketName}" thành công!`)
        setBucketName("")
      } else {
        setError(`Không thể tạo bucket: ${data.error || "Lỗi không xác định"}`)
      }
    } catch (err) {
      console.error("Lỗi khi tạo bucket:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsCreatingBucket(false)
    }
  }

  // Khởi tạo bảng
  const initializeTables = async () => {
    setIsInitializingTables(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/initialize-tables", {
        method: "POST",
      })
      const data = await response.json()

      setTablesStatus(data)

      if (data.success) {
        setSuccess("Đã khởi tạo thành công các bảng Supabase!")
      } else {
        setError(`Không thể khởi tạo bảng Supabase: ${data.error || "Lỗi không xác định"}`)
      }
    } catch (err) {
      console.error("Lỗi khi khởi tạo bảng:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsInitializingTables(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Cấu hình Supabase</h1>
          <p className="text-gray-500 mt-2">Cập nhật và khởi tạo Supabase cho dự án của bạn</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="connection">
        <TabsList>
          <TabsTrigger value="connection">Kết nối</TabsTrigger>
          <TabsTrigger value="buckets">Khởi tạo Buckets</TabsTrigger>
          <TabsTrigger value="single-bucket">Tạo Bucket Đơn Lẻ</TabsTrigger>
          <TabsTrigger value="tables">Khởi tạo Bảng</TabsTrigger>
          <TabsTrigger value="sql">SQL Script</TabsTrigger>
          <TabsTrigger value="config">Cấu hình</TabsTrigger>
        </TabsList>

        <TabsContent value="connection" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Kiểm tra kết nối Supabase</CardTitle>
              <CardDescription>Kiểm tra kết nối đến Supabase với thông tin đã cấu hình</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mb-4">
                <h3 className="font-medium text-amber-800 mb-2 flex items-center">
                  <Database className="mr-2 h-4 w-4" /> Thông tin kết nối Supabase
                </h3>
                <p className="text-sm text-amber-700 mb-2">Ứng dụng đang sử dụng các thông tin kết nối Supabase sau:</p>
                <pre className="bg-amber-100 p-2 rounded text-xs overflow-auto">
                  {`SUPABASE_URL=https://oklfvgcouhwrjtwyttjm.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://oklfvgcouhwrjtwyttjm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`}
                </pre>
              </div>

              <Button onClick={checkConnection} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang kiểm tra...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" /> Kiểm tra kết nối
                  </>
                )}
              </Button>
            </CardContent>
            {connectionStatus && (
              <CardFooter>
                <div className="w-full">
                  <h3 className="font-medium mb-2">Kết quả kiểm tra:</h3>
                  <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60 w-full">
                    {JSON.stringify(connectionStatus, null, 2)}
                  </pre>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="buckets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Khởi tạo Buckets</CardTitle>
              <CardDescription>Tạo các bucket storage cần thiết cho ứng dụng portfolio</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mb-4">
                <h3 className="font-medium text-amber-800 mb-2">Lưu ý quan trọng</h3>
                <p className="text-sm text-amber-700">
                  Quá trình này sẽ tạo các bucket storage cần thiết cho ứng dụng portfolio. Nếu các bucket đã tồn tại,
                  chúng sẽ không bị thay đổi.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-4">
                <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" /> Phương pháp thay thế
                </h3>
                <p className="text-sm text-blue-700 mb-2">
                  Nếu bạn gặp lỗi khi sử dụng nút bên dưới, vui lòng sử dụng tab "Tạo Bucket Đơn Lẻ" để tạo từng bucket
                  riêng biệt.
                </p>
              </div>

              <Button onClick={initializeSupabase} disabled={isInitializing}>
                {isInitializing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang khởi tạo...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" /> Khởi tạo Buckets
                  </>
                )}
              </Button>
            </CardContent>
            {initializeStatus && (
              <CardFooter>
                <div className="w-full">
                  <h3 className="font-medium mb-2">Kết quả khởi tạo:</h3>
                  <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60 w-full">
                    {JSON.stringify(initializeStatus, null, 2)}
                  </pre>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="single-bucket" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tạo Bucket Đơn Lẻ</CardTitle>
              <CardDescription>Tạo một bucket storage riêng lẻ</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mb-4">
                  <h3 className="font-medium text-amber-800 mb-2">Các bucket cần thiết</h3>
                  <p className="text-sm text-amber-700 mb-2">
                    Ứng dụng portfolio cần các bucket sau. Hãy tạo từng bucket một:
                  </p>
                  <ul className="list-disc pl-5 text-sm text-amber-700">
                    <li>avatars - Lưu trữ ảnh đại diện</li>
                    <li>projects - Lưu trữ ảnh dự án</li>
                    <li>technologies - Lưu trữ biểu tượng công nghệ</li>
                    <li>general - Lưu trữ các file chung</li>
                  </ul>
                </div>

                <div className="grid gap-4">
                  <div className="grid grid-cols-4 gap-4">
                    <div className="col-span-3">
                      <Label htmlFor="bucket-name">Tên bucket</Label>
                      <Input
                        id="bucket-name"
                        placeholder="Nhập tên bucket (ví dụ: avatars)"
                        value={bucketName}
                        onChange={(e) => setBucketName(e.target.value)}
                      />
                    </div>
                    <div className="flex items-end">
                      <Button onClick={createSingleBucket} disabled={isCreatingBucket} className="w-full">
                        {isCreatingBucket ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang tạo...
                          </>
                        ) : (
                          <>
                            <Plus className="mr-2 h-4 w-4" /> Tạo Bucket
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            {bucketStatus && (
              <CardFooter>
                <div className="w-full">
                  <h3 className="font-medium mb-2">Kết quả tạo bucket:</h3>
                  <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60 w-full">
                    {JSON.stringify(bucketStatus, null, 2)}
                  </pre>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="tables" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Khởi tạo Bảng</CardTitle>
              <CardDescription>Tạo các bảng dữ liệu cần thiết cho ứng dụng portfolio</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mb-4">
                <h3 className="font-medium text-amber-800 mb-2 flex items-center">
                  <Table className="mr-2 h-4 w-4" /> Các bảng sẽ được tạo
                </h3>
                <p className="text-sm text-amber-700 mb-2">
                  Quá trình này sẽ tạo các bảng dữ liệu sau nếu chúng chưa tồn tại:
                </p>
                <ul className="list-disc pl-5 text-sm text-amber-700">
                  <li>personal_info - Thông tin cá nhân</li>
                  <li>technologies - Công nghệ và kỹ năng</li>
                  <li>projects - Dự án</li>
                  <li>project_technologies - Liên kết giữa dự án và công nghệ</li>
                  <li>social_links - Liên kết mạng xã hội</li>
                  <li>settings - Cài đặt hệ thống</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-4">
                <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" /> Phương pháp thay thế
                </h3>
                <p className="text-sm text-blue-700 mb-2">
                  Nếu bạn gặp lỗi khi sử dụng nút bên dưới, vui lòng sử dụng tab "SQL Script" để tạo bảng thủ công thông
                  qua SQL Editor trong Supabase Dashboard.
                </p>
              </div>

              <Button onClick={initializeTables} disabled={isInitializingTables}>
                {isInitializingTables ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang khởi tạo bảng...
                  </>
                ) : (
                  <>
                    <Table className="mr-2 h-4 w-4" /> Khởi tạo Bảng
                  </>
                )}
              </Button>
            </CardContent>
            {tablesStatus && (
              <CardFooter>
                <div className="w-full">
                  <h3 className="font-medium mb-2">Kết quả khởi tạo bảng:</h3>
                  <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60 w-full">
                    {JSON.stringify(tablesStatus, null, 2)}
                  </pre>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="sql" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SQL Script</CardTitle>
              <CardDescription>Sử dụng SQL script này để tạo bảng thủ công trong Supabase SQL Editor</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-4">
                <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                  <Code className="mr-2 h-4 w-4" /> Hướng dẫn sử dụng
                </h3>
                <p className="text-sm text-blue-700 mb-2">
                  Sao chép SQL script bên dưới và dán vào SQL Editor trong Supabase Dashboard để tạo các bảng cần thiết:
                </p>
                <ol className="list-decimal pl-5 text-sm text-blue-700">
                  <li>
                    Đăng nhập vào{" "}
                    <a
                      href="https://app.supabase.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline inline-flex items-center"
                    >
                      Supabase Dashboard <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </li>
                  <li>Chọn project của bạn (oklfvgcouhwrjtwyttjm)</li>
                  <li>Đi đến "SQL Editor"</li>
                  <li>Nhấp vào "New Query"</li>
                  <li>Dán SQL script bên dưới</li>
                  <li>Nhấp vào "Run" để thực thi</li>
                </ol>
              </div>

              <div className="relative">
                <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-96 w-full">{sqlScript}</pre>
                <Button
                  size="sm"
                  variant="outline"
                  className="absolute top-2 right-2"
                  onClick={copySqlScript}
                  disabled={copied}
                >
                  {copied ? (
                    <>
                      <Check className="mr-2 h-4 w-4" /> Đã sao chép
                    </>
                  ) : (
                    <>
                      <Copy className="mr-2 h-4 w-4" /> Sao chép
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cấu hình môi trường</CardTitle>
              <CardDescription>Thông tin cấu hình môi trường cho Supabase</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                <h3 className="font-medium mb-2">Biến môi trường</h3>
                <p className="text-sm text-gray-700 mb-4">
                  Để ứng dụng hoạt động đúng, bạn cần đảm bảo các biến môi trường sau đã được cấu hình:
                </p>
                <pre className="bg-gray-100 p-3 rounded-md text-xs overflow-auto">
                  {`# Supabase URL
NEXT_PUBLIC_SUPABASE_URL=https://oklfvgcouhwrjtwyttjm.supabase.co
SUPABASE_URL=https://oklfvgcouhwrjtwyttjm.supabase.co

# Supabase Anon Key (Public)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk1MTAyMzUsImV4cCI6MjA1NTA4NjIzNX0.1yBxPl3uNFQRNHJzZNUCsc7Bm_2Ew20mCkZXIcC2z8o
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk1MTAyMzUsImV4cCI6MjA1NTA4NjIzNX0.1yBxPl3uNFQRNHJzZNUCsc7Bm_2Ew20mCkZXIcC2z8o

# Supabase Service Role Key (Private)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY

# Supabase JWT Secret
SUPABASE_JWT_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
