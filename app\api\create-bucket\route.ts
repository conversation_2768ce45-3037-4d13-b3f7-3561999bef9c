import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { config } from "@/lib/config"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "portfolio"

    const supabase = await createServerSupabaseClient()

    // Thử tạo bucket
    try {
      console.log(`Đang thử tạo bucket '${bucketName}'...`)

      const { data, error } = await supabase.storage.createBucket(bucketName, {
        public: true,
        fileSizeLimit: config.storage.defaultFileSizeLimit,
      })

      if (error) {
        // Nếu lỗi là do bucket đã tồn tại, đây là kết quả tốt
        if (error.message && error.message.includes("already exists")) {
          console.log(`Bucket '${bucketName}' đã tồn tại, đang cập nhật quyền...`)

          // Cập nhật quyền cho bucket
          try {
            const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
              public: true,
              fileSizeLimit: config.storage.defaultFileSizeLimit,
            })

            if (updateError) {
              console.error("Lỗi khi cập nhật quyền bucket:", updateError)
              return NextResponse.json(
                {
                  success: false,
                  error: `Lỗi khi cập nhật quyền bucket: ${updateError.message}`,
                },
                { status: 500 },
              )
            }

            console.log(`Bucket '${bucketName}' đã được cập nhật thành công`)
            return NextResponse.json({
              success: true,
              message: `Bucket '${bucketName}' đã tồn tại và được cập nhật thành công`,
              action: "updated",
            })
          } catch (updateErr) {
            console.error("Lỗi khi cập nhật bucket:", updateErr)
            return NextResponse.json(
              {
                success: false,
                error: `Lỗi khi cập nhật bucket: ${
                  updateErr instanceof Error ? updateErr.message : "Lỗi không xác định"
                }`,
              },
              { status: 500 },
            )
          }
        } else {
          // Lỗi khác khi tạo bucket
          console.error("Lỗi khi tạo bucket:", error)
          return NextResponse.json(
            {
              success: false,
              error: `Lỗi khi tạo bucket: ${error.message}`,
            },
            { status: 500 },
          )
        }
      }

      console.log(`Bucket '${bucketName}' đã được tạo thành công`)
      return NextResponse.json({
        success: true,
        message: `Bucket '${bucketName}' đã được tạo thành công`,
        action: "created",
      })
    } catch (error) {
      console.error("Lỗi khi tạo bucket:", error)
      return NextResponse.json(
        {
          success: false,
          error: `Lỗi khi tạo bucket: ${error instanceof Error ? error.message : "Lỗi không xác định"}`,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Lỗi trong API create-bucket:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}


