import { createClient } from "@supabase/supabase-js"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    // Tạo Supabase client với service role key để có quyền admin
    const supabaseUrl = "https://oklfvgcouhwrjtwyttjm.supabase.co"
    const supabaseServiceKey =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY"

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Danh sách các bảng cần tắt RLS
    const tables = ["personal_info", "technologies", "projects", "project_technologies", "social_links", "settings"]

    const results = []

    // Tắt RLS cho từng bảng
    for (const table of tables) {
      try {
        const { data, error } = await supabase.rpc("disable_rls", { table_name: table })

        if (error) {
          // Nếu rpc không hoạt động, thử thực thi SQL trực tiếp
          const { data: sqlData, error: sqlError } = await supabase
            .from("_disable_rls_direct")
            .select("*")
            .eq("table_name", table)
            .single()

          if (sqlError) {
            results.push({
              table,
              success: false,
              error: sqlError.message,
              note: "Bạn cần thực hiện thủ công trong SQL Editor",
            })
          } else {
            results.push({
              table,
              success: true,
              message: "Đã tắt RLS thông qua SQL trực tiếp",
            })
          }
        } else {
          results.push({
            table,
            success: true,
            message: "Đã tắt RLS thành công",
          })
        }
      } catch (err) {
        results.push({
          table,
          success: false,
          error: err instanceof Error ? err.message : "Lỗi không xác định",
          note: "Bạn cần thực hiện thủ công trong SQL Editor",
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: "Đã hoàn thành quá trình tắt RLS",
      results,
      sql_script: `
-- Tắt RLS cho tất cả các bảng
ALTER TABLE personal_info DISABLE ROW LEVEL SECURITY;
ALTER TABLE technologies DISABLE ROW LEVEL SECURITY;
ALTER TABLE projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE project_technologies DISABLE ROW LEVEL SECURITY;
ALTER TABLE social_links DISABLE ROW LEVEL SECURITY;
ALTER TABLE settings DISABLE ROW LEVEL SECURITY;

-- Tạo policy cho phép truy cập đầy đủ (nếu bạn muốn giữ RLS nhưng cho phép truy cập)
-- CREATE POLICY "Allow full access" ON personal_info FOR ALL USING (true);
-- CREATE POLICY "Allow full access" ON technologies FOR ALL USING (true);
-- CREATE POLICY "Allow full access" ON projects FOR ALL USING (true);
-- CREATE POLICY "Allow full access" ON project_technologies FOR ALL USING (true);
-- CREATE POLICY "Allow full access" ON social_links FOR ALL USING (true);
-- CREATE POLICY "Allow full access" ON settings FOR ALL USING (true);
      `,
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
