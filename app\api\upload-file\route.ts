import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { ensureStorageBucket } from "@/utils/supabase/storage-utils"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    console.log("Bắt đầu xử lý upload file...")

    // <PERSON><PERSON><PERSON> bảo request là multipart/form-data
    const formData = await request.formData()
    const file = formData.get("file") as File
    const bucketName = (formData.get("bucketName") as string) || "general" // Mặc định là bucket general
    const folderPath = (formData.get("folderPath") as string) || "images"

    console.log(`Thông tin upload: bucket=${bucketName}, folder=${folderPath}, fileName=${file?.name}`)

    if (!file) {
      console.error("Không có file được cung cấp trong request")
      return NextResponse.json({
        success: false,
        error: "Không có file được cung cấp"
      }, { status: 400 })
    }

    // <PERSON><PERSON><PERSON> tra kích thước file
    if (file.size > 10 * 1024 * 1024) { // 10MB
      console.error(`File quá lớn: ${file.size} bytes`)
      return NextResponse.json({
        success: false,
        error: "File quá lớn, kích thước tối đa là 10MB"
      }, { status: 400 })
    }

    // Đảm bảo bucket tồn tại
    console.log(`Đang đảm bảo bucket '${bucketName}' tồn tại...`)
    const bucketResult = await ensureStorageBucket(bucketName)

    if (!bucketResult.success) {
      console.error(`Không thể đảm bảo bucket tồn tại: ${bucketResult.error}`)
      return NextResponse.json({
        success: false,
        error: `Không thể đảm bảo bucket tồn tại: ${bucketResult.error}`
      }, { status: 500 })
    }

    if (bucketResult.warning) {
      console.warn(`Cảnh báo khi đảm bảo bucket: ${bucketResult.warning}`)
    }

    // Lấy Supabase client với service role để có quyền upload
    const supabase = createServiceSupabaseClient()
    if (!supabase) {
      console.error("Không thể khởi tạo Supabase client")
      return NextResponse.json({
        success: false,
        error: "Không thể khởi tạo Supabase client"
      }, { status: 500 })
    }

    // Chuẩn bị file để upload
    const fileExt = file.name.split(".").pop()
    const fileName = `${Date.now()}.${fileExt}`
    const filePath = `${folderPath}/${fileName}`
    console.log(`Đường dẫn file: ${filePath}`)

    try {
      // Chuyển đổi File thành ArrayBuffer
      console.log("Đang chuyển đổi file thành ArrayBuffer...")
      const arrayBuffer = await file.arrayBuffer()
      const fileBuffer = new Uint8Array(arrayBuffer)

      // Upload file
      console.log(`Đang upload file vào bucket '${bucketName}'...`)
      const { data, error } = await supabase.storage.from(bucketName).upload(filePath, fileBuffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: file.type,
      })

      if (error) {
        console.error("Chi tiết lỗi upload:", error)
        return NextResponse.json({
          success: false,
          error: `Upload thất bại: ${error.message}`,
          code: error.code,
          details: error
        }, { status: 500 })
      }

      // Lấy public URL
      console.log("Upload thành công, đang lấy public URL...")
      const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)

      console.log(`Hoàn tất upload: ${urlData.publicUrl}`)
      return NextResponse.json({
        success: true,
        filePath,
        publicUrl: urlData.publicUrl,
      })
    } catch (uploadError) {
      console.error("Lỗi khi xử lý hoặc upload file:", uploadError)
      return NextResponse.json({
        success: false,
        error: uploadError instanceof Error
          ? `Lỗi khi xử lý hoặc upload file: ${uploadError.message}`
          : "Lỗi không xác định khi xử lý hoặc upload file",
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Lỗi trong route upload-file:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định trong quá trình upload",
      },
      { status: 500 },
    )
  }
}

// Cấu hình để cho phép upload file lớn
export const config = {
  api: {
    bodyParser: false,
  },
}
