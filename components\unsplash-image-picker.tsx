"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Download, ExternalLink, Check, Loader2, Image as ImageIcon, X } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface UnsplashImage {
  id: string
  urls: {
    small: string
    regular: string
    full: string
    thumb: string
  }
  alt_description: string | null
  description: string | null
  user: {
    name: string
    username: string
  }
  links: {
    html: string
  }
  width: number
  height: number
}

interface UnsplashImagePickerProps {
  onImageSelected: (imageUrl: string, imageData?: UnsplashImage) => void
  currentImageUrl?: string
  className?: string
  searchPlaceholder?: string
  defaultQuery?: string
}

export default function UnsplashImagePicker({
  onImageSelected,
  currentImageUrl,
  className,
  searchPlaceholder = "Search for images...",
  defaultQuery = "technology"
}: UnsplashImagePickerProps) {
  const [searchQuery, setSearchQuery] = useState(defaultQuery)
  const [images, setImages] = useState<UnsplashImage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<string | null>(currentImageUrl || null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // Load initial images
  useEffect(() => {
    if (defaultQuery) {
      searchImages(defaultQuery, 1)
    }
  }, [defaultQuery])

  // Sync selected image with current URL
  useEffect(() => {
    setSelectedImage(currentImageUrl || null)
  }, [currentImageUrl])

  const searchImages = async (query: string, pageNum: number = 1) => {
    if (!query.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/unsplash/search?query=${encodeURIComponent(query)}&page=${pageNum}&per_page=12`)
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (pageNum === 1) {
        setImages(data.results || [])
      } else {
        setImages(prev => [...prev, ...(data.results || [])])
      }
      
      setHasMore(data.total_pages > pageNum)
      setPage(pageNum)
    } catch (err) {
      console.error("Unsplash search error:", err)
      setError(err instanceof Error ? err.message : "Failed to search images")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      searchImages(searchQuery.trim(), 1)
    }
  }

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      searchImages(searchQuery, page + 1)
    }
  }

  const handleImageSelect = (image: UnsplashImage) => {
    const imageUrl = image.urls.regular
    setSelectedImage(imageUrl)
    onImageSelected(imageUrl, image)
  }

  const handleRemoveImage = () => {
    setSelectedImage(null)
    onImageSelected("")
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        <div>
          <Label htmlFor="unsplash-search">Search Unsplash Images</Label>
          <form onSubmit={handleSearch} className="flex gap-2 mt-1">
            <Input
              id="unsplash-search"
              type="text"
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading}>
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Selected Image */}
        {selectedImage && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Selected Image:</Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveImage}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              </div>
              <div className="relative aspect-video w-full max-w-sm overflow-hidden rounded-lg border border-gray-200">
                <img
                  src={selectedImage}
                  alt="Selected"
                  className="h-full w-full object-cover"
                />
                <div className="absolute top-2 right-2">
                  <Badge className="bg-green-500 text-white">
                    <Check className="h-3 w-3 mr-1" />
                    Selected
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Image Grid */}
        {images.length > 0 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {images.map((image) => (
                <Card
                  key={image.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedImage === image.urls.regular ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => handleImageSelect(image)}
                >
                  <CardContent className="p-2">
                    <div className="relative aspect-video overflow-hidden rounded">
                      <img
                        src={image.urls.small}
                        alt={image.alt_description || image.description || "Unsplash image"}
                        className="h-full w-full object-cover"
                      />
                      {selectedImage === image.urls.regular && (
                        <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                          <Badge className="bg-blue-500 text-white">
                            <Check className="h-3 w-3 mr-1" />
                            Selected
                          </Badge>
                        </div>
                      )}
                    </div>
                    <div className="mt-2 text-xs text-gray-600">
                      <p className="font-medium truncate">by {image.user.name}</p>
                      <p className="text-gray-500">{image.width} × {image.height}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Load More Images
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        )}

        {/* No Results */}
        {!isLoading && images.length === 0 && searchQuery && (
          <div className="text-center py-8 text-gray-500">
            <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No images found for "{searchQuery}"</p>
            <p className="text-xs">Try a different search term</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && images.length === 0 && (
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-500">Searching images...</p>
          </div>
        )}
      </div>
    </div>
  )
}
