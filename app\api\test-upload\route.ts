import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "portfolio"

    const supabase = await createServerSupabaseClient()

    // Tạo một file test
    const testContent = `Test file created at ${new Date().toISOString()}`
    const testFileName = `test-${Date.now()}.txt`
    const filePath = `test/${testFileName}`

    // Thử upload file test
    try {
      console.log(`<PERSON>ang thử upload file test vào bucket '${bucketName}'...`)

      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, new Blob([testContent]), { upsert: true })

      if (error) {
        console.error("Lỗi khi upload file test:", error)
        return NextResponse.json(
          {
            success: false,
            error: `Lỗi khi upload file test: ${error.message}`,
          },
          { status: 500 },
        )
      }

      // Lấy URL công khai
      const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)

      console.log(`Đã upload file test thành công: ${filePath}`)
      return NextResponse.json({
        success: true,
        message: `Đã upload file test thành công: ${filePath}`,
        filePath,
        publicUrl: urlData.publicUrl,
      })
    } catch (error) {
      console.error("Lỗi khi upload file test:", error)
      return NextResponse.json(
        {
          success: false,
          error: `Lỗi khi upload file test: ${error instanceof Error ? error.message : "Lỗi không xác định"}`,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Lỗi trong API test-upload:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
