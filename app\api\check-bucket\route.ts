import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "portfolio"

    const supabase = await createServerSupabaseClient()

    // Kiểm tra xem bucket có tồn tại không
    try {
      // Thử lấy thông tin bucket
      const { data, error } = await supabase.storage.getBucket(bucketName)

      if (error) {
        // Nếu lỗi không phải là "không tìm thấy", ghi lại lỗi
        if (!error.message.includes("not found")) {
          console.error(`Lỗi khi lấy thông tin bucket '${bucketName}':`, error)
        }

        // Thử phương pháp khác: kiểm tra bằng cách liệt kê các file
        try {
          const { data: files, error: listError } = await supabase.storage.from(bucketName).list()

          if (listError) {
            return NextResponse.json({
              exists: false,
              error: listError.message,
              method: "list",
            })
          }

          // Nếu có thể liệt kê file, bucket tồn tại
          return NextResponse.json({
            exists: true,
            method: "list",
            files: files?.length || 0,
          })
        } catch (listError) {
          console.error(`Lỗi khi liệt kê file trong bucket '${bucketName}':`, listError)
          return NextResponse.json({
            exists: false,
            error: "Không thể xác minh bucket bằng cách liệt kê file",
            method: "list",
            listError: listError instanceof Error ? listError.message : "Lỗi không xác định",
          })
        }
      }

      // Bucket tồn tại
      return NextResponse.json({
        exists: true,
        bucket: data,
        method: "getBucket",
      })
    } catch (error) {
      console.error(`Lỗi khi kiểm tra bucket '${bucketName}':`, error)
      return NextResponse.json({
        exists: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
        method: "exception",
      })
    }
  } catch (error) {
    console.error("Lỗi trong API check-bucket:", error)
    return NextResponse.json(
      {
        exists: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
