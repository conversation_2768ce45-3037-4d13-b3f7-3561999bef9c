import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    // Ki<PERSON>m tra kết nối đến Supabase
    let connection = false
    try {
      const { data: healthCheck, error: healthError } = await supabase.rpc("get_service_status")
      connection = !healthError && !!healthCheck
    } catch (err) {
      console.error("Lỗi khi kiểm tra kết nối Supabase:", err)
      connection = false
    }

    // Kiểm tra Storage API
    let storageEnabled = false
    let buckets = []
    let storageError = null

    try {
      const { data, error } = await supabase.storage.listBuckets()

      if (error) {
        console.error("Lỗi khi liệt kê buckets:", error)
        storageError = error.message
        storageEnabled = false
      } else {
        storageEnabled = true
        buckets = data || []
      }
    } catch (err) {
      console.error("Lỗi khi truy cập Storage API:", err)
      storageError = err instanceof Error ? err.message : "Lỗi không xác định"
      storageEnabled = false
    }

    // Kiểm tra thông tin project
    let projectInfo = null
    try {
      const { data: project, error: projectError } = await supabase.rpc("get_project_info")

      if (!projectError) {
        projectInfo = project
      }
    } catch (err) {
      console.error("Lỗi khi lấy thông tin project:", err)
    }

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      connection,
      storageEnabled,
      buckets,
      storageError,
      projectInfo,
      supabaseUrl: process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL,
    })
  } catch (error) {
    console.error("Lỗi khi kiểm tra trạng thái Storage:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
