# Hướng dẫn tạo bảng Supabase thủ công

## Tổng quan

Nếu API tự động không thể tạo bảng, bạn cần tạo chúng thủ công trong Supabase Dashboard. Tài liệu này hướng dẫn chi tiết cách tạo từng bảng.

## C<PERSON>ch truy cập Supabase Dashboard

1. T<PERSON>y cập: https://supabase.com/dashboard
2. Đăng nhập vào tài khoản của bạn
3. Chọn project portfolio của bạn
4. Vào **Table Editor** từ sidebar

## Tạo bảng bằng SQL Editor (Khuyến nghị)

### Bước 1: Mở SQL Editor
1. Trong Supabase Dashboard, click **SQL Editor** từ sidebar
2. Click **New query**

### Bước 2: Chạy script tạo bảng
Copy và paste đoạn SQL sau vào editor, sau đ<PERSON> click **Run**:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Create personal_info table
CREATE TABLE IF NOT EXISTS personal_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    bio TEXT,
    location TEXT,
    website TEXT,
    email TEXT NOT NULL,
    profile_image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create technologies table
CREATE TABLE IF NOT EXISTS technologies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create project_tags table
CREATE TABLE IF NOT EXISTS project_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, tag)
);

-- 5. Create social_links table
CREATE TABLE IF NOT EXISTS social_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform TEXT NOT NULL,
    url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(platform)
);

-- 6. Create github_stats table
CREATE TABLE IF NOT EXISTS github_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stars INTEGER DEFAULT 0,
    repos INTEGER DEFAULT 0,
    followers INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_project_tags_project_id ON project_tags(project_id);
CREATE INDEX IF NOT EXISTS idx_technologies_name ON technologies(name);
CREATE INDEX IF NOT EXISTS idx_social_links_platform ON social_links(platform);
```

## Tạo bảng bằng Table Editor (Thủ công)

Nếu SQL Editor không hoạt động, bạn có thể tạo từng bảng thủ công:

### 1. Bảng `personal_info`

1. Trong **Table Editor**, click **New table**
2. Table name: `personal_info`
3. Thêm các columns:

| Column Name | Type | Default | Nullable | Primary Key |
|-------------|------|---------|----------|-------------|
| id | uuid | uuid_generate_v4() | No | Yes |
| name | text | - | No | No |
| role | text | - | No | No |
| bio | text | - | Yes | No |
| location | text | - | Yes | No |
| website | text | - | Yes | No |
| email | text | - | No | No |
| profile_image | text | - | Yes | No |
| created_at | timestamptz | now() | No | No |
| updated_at | timestamptz | now() | No | No |

### 2. Bảng `technologies`

| Column Name | Type | Default | Nullable | Primary Key | Unique |
|-------------|------|---------|----------|-------------|--------|
| id | uuid | uuid_generate_v4() | No | Yes | No |
| name | text | - | No | No | Yes |
| created_at | timestamptz | now() | No | No | No |

### 3. Bảng `projects`

| Column Name | Type | Default | Nullable | Primary Key |
|-------------|------|---------|----------|-------------|
| id | uuid | uuid_generate_v4() | No | Yes |
| title | text | - | No | No |
| description | text | - | Yes | No |
| image_url | text | - | Yes | No |
| created_at | timestamptz | now() | No | No |
| updated_at | timestamptz | now() | No | No |

### 4. Bảng `project_tags`

| Column Name | Type | Default | Nullable | Primary Key | Foreign Key |
|-------------|------|---------|----------|-------------|-------------|
| id | uuid | uuid_generate_v4() | No | Yes | No |
| project_id | uuid | - | No | No | projects(id) |
| tag | text | - | No | No | No |
| created_at | timestamptz | now() | No | No | No |

**Unique constraint:** (project_id, tag)

### 5. Bảng `social_links`

| Column Name | Type | Default | Nullable | Primary Key | Unique |
|-------------|------|---------|----------|-------------|--------|
| id | uuid | uuid_generate_v4() | No | Yes | No |
| platform | text | - | No | No | Yes |
| url | text | - | No | No | No |
| created_at | timestamptz | now() | No | No | No |
| updated_at | timestamptz | now() | No | No | No |

### 6. Bảng `github_stats`

| Column Name | Type | Default | Nullable | Primary Key |
|-------------|------|---------|----------|-------------|
| id | uuid | uuid_generate_v4() | No | Yes |
| stars | int4 | 0 | No | No |
| repos | int4 | 0 | No | No |
| followers | int4 | 0 | No | No |
| created_at | timestamptz | now() | No | No |
| updated_at | timestamptz | now() | No | No |

## Kiểm tra sau khi tạo

1. Truy cập `/admin/table-status` để kiểm tra tất cả bảng đã được tạo
2. Nếu tất cả bảng hiển thị "Exists", truy cập `/admin/database-migration`
3. Chạy "Setup Database with Sample Data" để thêm dữ liệu mẫu
4. Kiểm tra trang chủ để xem dữ liệu hiển thị

## Troubleshooting

### Lỗi "uuid_generate_v4() function not found"
Chạy lệnh này trong SQL Editor:
```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

### Lỗi Foreign Key
Đảm bảo bạn tạo bảng `projects` trước khi tạo `project_tags`

### Lỗi Unique Constraint
Nếu bạn đã có dữ liệu trùng lặp, xóa dữ liệu cũ trước khi thêm constraint

## Kết luận

Sau khi tạo xong tất cả bảng:
1. Tất cả 6 bảng sẽ hiển thị "Exists" trong Table Status
2. Bạn có thể chạy Database Setup để thêm dữ liệu mẫu
3. Trang chủ sẽ hiển thị đầy đủ thông tin từ database

Nếu gặp vấn đề, hãy kiểm tra logs trong browser console hoặc Supabase Dashboard.
