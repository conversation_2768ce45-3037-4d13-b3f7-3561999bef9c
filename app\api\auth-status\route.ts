import { createServerSupabaseClient, createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const results = {
      userAuth: null as any,
      serviceAuth: null as any,
      storageAccess: null as any,
      uploadPermissions: null as any
    }

    // 1. Check user authentication (from cookies/session)
    try {
      const userSupabase = await createServerSupabaseClient()
      const { data: { user }, error: userError } = await userSupabase.auth.getUser()
      
      results.userAuth = {
        success: !userError && !!user,
        user: user ? {
          id: user.id,
          email: user.email,
          email_confirmed_at: user.email_confirmed_at
        } : null,
        error: userError?.message
      }
    } catch (error) {
      results.userAuth = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 2. Check service role authentication
    try {
      const serviceSupabase = createServiceSupabaseClient()
      const { data: buckets, error: serviceError } = await serviceSupabase.storage.listBuckets()
      
      results.serviceAuth = {
        success: !serviceError,
        bucketsCount: buckets?.length || 0,
        error: serviceError?.message
      }
    } catch (error) {
      results.serviceAuth = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 3. Check storage access specifically
    try {
      const serviceSupabase = createServiceSupabaseClient()
      
      // Check if 'general' bucket exists
      const { data: buckets, error: listError } = await serviceSupabase.storage.listBuckets()
      const generalBucket = buckets?.find(bucket => bucket.name === 'general')
      
      results.storageAccess = {
        success: !listError,
        generalBucketExists: !!generalBucket,
        totalBuckets: buckets?.length || 0,
        buckets: buckets?.map(b => ({ name: b.name, public: b.public })) || [],
        error: listError?.message
      }
    } catch (error) {
      results.storageAccess = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 4. Test upload permissions
    try {
      const serviceSupabase = createServiceSupabaseClient()
      const testContent = `Auth test ${Date.now()}`
      const testFileName = `auth-test-${Date.now()}.txt`
      
      const { error: uploadError } = await serviceSupabase.storage
        .from('general')
        .upload(`test/${testFileName}`, new Blob([testContent]), { upsert: true })

      if (!uploadError) {
        // Clean up test file
        await serviceSupabase.storage.from('general').remove([`test/${testFileName}`])
      }

      results.uploadPermissions = {
        success: !uploadError,
        canUpload: !uploadError,
        error: uploadError?.message
      }
    } catch (error) {
      results.uploadPermissions = {
        success: false,
        canUpload: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // Overall status
    const overallSuccess = results.serviceAuth.success && 
                          results.storageAccess.success && 
                          results.uploadPermissions.success

    return NextResponse.json({
      success: overallSuccess,
      message: overallSuccess 
        ? "All authentication and storage checks passed"
        : "Some authentication or storage issues detected",
      details: results,
      recommendations: !overallSuccess ? [
        !results.serviceAuth.success && "Check Supabase service role key configuration",
        !results.storageAccess.success && "Verify storage access and bucket configuration", 
        !results.storageAccess.generalBucketExists && "Create 'general' bucket in Supabase Dashboard",
        !results.uploadPermissions.success && "Configure storage policies for upload permissions"
      ].filter(Boolean) : []
    })

  } catch (error) {
    console.error("Auth status check error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to check authentication status"
      },
      { status: 500 }
    )
  }
}
