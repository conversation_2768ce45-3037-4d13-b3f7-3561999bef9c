import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "default"

    // Tạo Supabase client với service role key để có quyền admin
    const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

    // Tạo bucket
    const { data, error } = await supabase.storage.createBucket(bucketName, {
      public: true,
      fileSizeLimit: 10485760, // 10MB
    })

    if (error && error.message !== "Bucket already exists") {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 500 },
      )
    }

    // Kiểm tra xem bucket đã tồn tại chưa
    const { data: bucketList, error: listError } = await supabase.storage.listBuckets()

    if (listError) {
      return NextResponse.json(
        {
          success: false,
          error: listError.message,
        },
        { status: 500 },
      )
    }

    const bucketExists = bucketList.some((b) => b.name === bucketName)

    return NextResponse.json({
      success: true,
      message: error ? "Bucket đã tồn tại" : "Bucket đã được tạo thành công",
      bucketExists,
      data,
    })
  } catch (error) {
    console.error("Lỗi khi tạo bucket:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}

