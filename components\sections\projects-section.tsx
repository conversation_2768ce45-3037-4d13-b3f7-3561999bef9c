import { Code } from "lucide-react"
import ProjectCard from "@/components/project-card"
import { type Project } from "@/actions/portfolio-actions"

interface ProjectsSectionProps {
  projects: Project[]
}

export default function ProjectsSection({ projects }: ProjectsSectionProps) {
  return (
    <section id="projects" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
      <h2 className="text-3xl font-bold mb-12 flex items-center gradient-text">
        <Code className="mr-2 h-6 w-6 animate-pulse" /> Selected Projects
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {projects.map((project, index) => (
          <div
            key={project.id}
            className="card-hover"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <ProjectCard
              title={project.title}
              description={project.description}
              tags={project.tags}
              imageUrl={project.imageUrl}
            />
          </div>
        ))}
      </div>
    </section>
  )
}
