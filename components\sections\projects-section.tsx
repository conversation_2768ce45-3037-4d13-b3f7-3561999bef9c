import { Code } from "lucide-react"
import ProjectCard from "@/components/project-card"
import { type Project } from "@/actions/portfolio-actions"

interface ProjectsSectionProps {
  projects: Project[]
}

export default function ProjectsSection({ projects }: ProjectsSectionProps) {
  return (
    <section id="projects" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
      <h2 className="text-3xl font-bold mb-12 flex items-center">
        <Code className="mr-2 h-6 w-6" /> Selected Projects
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {projects.map((project) => (
          <ProjectCard
            key={project.id}
            title={project.title}
            description={project.description}
            tags={project.tags}
            imageUrl={project.imageUrl}
          />
        ))}
      </div>
    </section>
  )
}
