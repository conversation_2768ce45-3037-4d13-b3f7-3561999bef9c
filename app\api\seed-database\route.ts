import { createServerSupabaseClient } from "@/utils/supabase/server"
import { ensureStorageBucket } from "@/utils/supabase/storage-utils"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()

    // Ensure storage bucket exists
    const bucketCreated = await ensureStorageBucket("portfolio")
    if (!bucketCreated) {
      return NextResponse.json({ message: "Error creating storage bucket" }, { status: 500 })
    }

    // Check if personal_info table is empty
    const { count: personalInfoCount, error: countError } = await supabase
      .from("personal_info")
      .select("*", { count: "exact", head: true })

    if (countError) {
      console.error("Error checking personal_info table:", countError)
      return NextResponse.json({ message: "Error checking database state" }, { status: 500 })
    }

    // If we already have data, don't seed
    if (personalInfoCount && personalInfoCount > 0) {
      return NextResponse.json({ message: "Database already has data" }, { status: 200 })
    }

    // Seed personal info
    const { error: personalInfoError } = await supabase.from("personal_info").insert({
      name: "Peanut",
      role: "Creative developer currently working on Blameo",
      bio: "I'm currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I'm currently exploring DevOps.",
      location: "Hanoi",
      website: "truongqa.com",
      email: "<EMAIL>",
      profile_image: "/placeholder.svg?height=500&width=500",
    })

    if (personalInfoError) {
      console.error("Error seeding personal_info:", personalInfoError)
      return NextResponse.json({ message: "Error seeding personal_info" }, { status: 500 })
    }

    // Seed technologies
    const technologies = [
      "React",
      "Next",
      "TypeScript",
      "JavaScript",
      "Node.js",
      "Vue.js",
      "HTML5",
      "CSS",
      "Tailwindcss",
      "Redux",
      "GraphQL",
      "AWS",
      "Firebase",
      "Docker",
      "Git",
      "MongoDB",
      "Express.js",
      "Vercel",
      "Chakra",
      "Figma",
      "Styled-components",
      "Sass",
      "Bootstrap",
      "Postgres",
    ]

    for (const tech of technologies) {
      const { error } = await supabase.from("technologies").insert({ name: tech })
      if (error) {
        console.error(`Error seeding technology ${tech}:`, error)
        // Continue with other technologies even if one fails
      }
    }

    // Seed projects
    const projects = [
      {
        title: "E-commerce Platform",
        description: "A modern e-commerce platform with a unique shopping experience",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "Tailwind", "Stripe"],
      },
      {
        title: "Portfolio Website",
        description: "A creative portfolio for a digital artist with interactive elements",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["React", "Framer Motion", "Three.js"],
      },
      {
        title: "Mobile App",
        description: "A lifestyle app with a clean, minimalist interface",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["React Native", "Firebase", "UI/UX"],
      },
      {
        title: "Dashboard UI",
        description: "An analytics dashboard with data visualization",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["TypeScript", "D3.js", "Tailwind"],
      },
      {
        title: "Blog Platform",
        description: "A content management system with a modern interface",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "Prisma", "PostgreSQL"],
      },
      {
        title: "Landing Page",
        description: "A conversion-focused landing page with animations",
        image_url: "/placeholder.svg?height=400&width=600",
        tags: ["HTML/CSS", "JavaScript", "GSAP"],
      },
    ]

    for (const project of projects) {
      const { data: newProject, error } = await supabase
        .from("projects")
        .insert({
          title: project.title,
          description: project.description,
          image_url: project.image_url,
        })
        .select("id")
        .single()

      if (error || !newProject) {
        console.error(`Error seeding project ${project.title}:`, error)
        continue
      }

      // Add tags for this project
      for (const tag of project.tags) {
        const { error: tagError } = await supabase.from("project_tags").insert({
          project_id: newProject.id,
          tag,
        })

        if (tagError) {
          console.error(`Error adding tag ${tag} to project ${project.title}:`, tagError)
          // Continue with other tags even if one fails
        }
      }
    }

    // Seed social links
    const socialLinks = [
      {
        platform: "GitHub",
        url: "https://github.com/truongnat",
      },
      {
        platform: "Facebook",
        url: "https://facebook.com",
      },
      {
        platform: "Medium",
        url: "https://medium.com",
      },
      {
        platform: "StackOverflow",
        url: "https://stackoverflow.com",
      },
    ]

    for (const link of socialLinks) {
      const { error } = await supabase.from("social_links").insert(link)
      if (error) {
        console.error(`Error seeding social link ${link.platform}:`, error)
        // Continue with other links even if one fails
      }
    }

    // Seed GitHub stats
    const { error: githubStatsError } = await supabase.from("github_stats").insert({
      stars: 31,
      repos: 16,
      followers: 13,
    })

    if (githubStatsError) {
      console.error("Error seeding github_stats:", githubStatsError)
      return NextResponse.json({ message: "Error seeding github_stats" }, { status: 500 })
    }

    return NextResponse.json({ message: "Database seeded successfully" }, { status: 200 })
  } catch (error) {
    console.error("Error in seed-database route:", error)
    return NextResponse.json({ message: "An unexpected error occurred" }, { status: 500 })
  }
}
