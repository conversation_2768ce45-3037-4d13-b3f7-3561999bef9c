import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function POST() {
  try {
    // Tạo Supabase client với service role key để có quyền admin
    const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

    // Tạo extension uuid-ossp nếu chưa có
    await supabase.rpc("create_uuid_extension").catch(() => {
      console.log("UUID extension đã tồn tại hoặc không thể tạo")
    })

    // Tạo các bảng
    const tables = [
      {
        name: "personal_info",
        query: `
          CREATE TABLE IF NOT EXISTS personal_info (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            title TEXT NOT NULL,
            bio TEXT,
            avatar_url TEXT,
            email TEXT,
            location TEXT,
            available_for_hire BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `,
      },
      {
        name: "technologies",
        query: `
          CREATE TABLE IF NOT EXISTS technologies (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            icon TEXT,
            category TEXT NOT NULL,
            proficiency INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `,
      },
      {
        name: "projects",
        query: `
          CREATE TABLE IF NOT EXISTS projects (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            description TEXT,
            image_url TEXT,
            github_url TEXT,
            live_url TEXT,
            featured BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `,
      },
      {
        name: "project_technologies",
        query: `
          CREATE TABLE IF NOT EXISTS project_technologies (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            technology_id UUID REFERENCES technologies(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(project_id, technology_id)
          );
        `,
      },
      {
        name: "social_links",
        query: `
          CREATE TABLE IF NOT EXISTS social_links (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            platform TEXT NOT NULL,
            url TEXT NOT NULL,
            icon TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `,
      },
      {
        name: "settings",
        query: `
          CREATE TABLE IF NOT EXISTS settings (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            key TEXT UNIQUE NOT NULL,
            value JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `,
      },
    ]

    // Thực hiện các truy vấn SQL
    const results = []
    for (const table of tables) {
      try {
        // Kiểm tra xem bảng đã tồn tại chưa
        const { data: existingData, error: existingError } = await supabase.from(table.name).select("*").limit(1)

        if (existingError && existingError.code === "42P01") {
          // Bảng chưa tồn tại, tạo mới bằng cách sử dụng SQL trực tiếp
          const { error } = await supabase.sql(table.query)

          if (error) {
            results.push({
              table: table.name,
              success: false,
              error: error.message,
            })
          } else {
            results.push({
              table: table.name,
              success: true,
              message: "Đã tạo bảng thành công",
            })
          }
        } else {
          // Bảng đã tồn tại
          results.push({
            table: table.name,
            success: true,
            message: "Bảng đã tồn tại",
          })
        }
      } catch (error) {
        results.push({
          table: table.name,
          success: false,
          error: error instanceof Error ? error.message : "Lỗi không xác định",
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: "Đã hoàn thành quá trình khởi tạo bảng",
      results,
    })
  } catch (error) {
    console.error("Lỗi khi khởi tạo bảng:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}

