// Centralized configuration for all environment variables
export const config = {
  supabase: {
    url: process.env.SUPABASE_URL || "https://oklfvgcouhwrjtwyttjm.supabase.co",
    publicUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || "https://oklfvgcouhwrjtwyttjm.supabase.co",
    anonKey: process.env.SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk1MTAyMzUsImV4cCI6MjA1NTA4NjIzNX0.1yBxPl3uNFQRNHJzZNUCsc7Bm_2Ew20mCkZXIcC2z8o",
    publicAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk1MTAyMzUsImV4cCI6MjA1NTA4NjIzNX0.1yBxPl3uNFQRNHJzZNUCsc7Bm_2Ew20mCkZXIcC2z8o",
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY",
    jwtSecret: process.env.SUPABASE_JWT_SECRET || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY",
    projectId: process.env.SUPABASE_PROJECT_ID || "oklfvgcouhwrjtwyttjm",
  },
  storage: {
    defaultBuckets: ["avatars", "projects", "technologies", "general"],
    defaultFileSizeLimit: 10485760, // 10MB
  }
}