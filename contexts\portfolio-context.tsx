"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, type ReactNode } from "react"
import {
  fetchPortfolioData,
  updatePersonalInfo as updatePersonalInfoAction,
  updateTechnologies as updateTechnologiesAction,
  addProject as addProjectAction,
  updateProject as updateProjectAction,
  deleteProject as deleteProjectAction,
  updateSocialLinks as updateSocialLinksAction,
  updateGithubStats as updateGithubStatsAction,
  type PersonalInfo,
  type Project,
  type SocialLink,
  type GithubStats,
} from "@/actions/portfolio-actions"
import { generateFallbackData } from "@/utils/fallback-data"

interface PortfolioData {
  personalInfo: PersonalInfo
  technologies: string[]
  projects: Project[]
  socialLinks: SocialLink[]
  githubStats: GithubStats
}

interface PortfolioContextType {
  data: PortfolioData
  isLoading: boolean
  error: string | null
  usingFallback: boolean
  updatePersonalInfo: (info: PersonalInfo) => Promise<void>
  updateTechnologies: (techs: string[]) => Promise<void>
  addProject: (project: Omit<Project, "id">) => Promise<string | null>
  updateProject: (project: Project) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  updateSocialLinks: (links: SocialLink[]) => Promise<void>
  updateGithubStats: (stats: GithubStats) => Promise<void>
  refreshData: () => Promise<void>
}

// Default data from fallback
const defaultData = generateFallbackData()

const PortfolioContext = createContext<PortfolioContextType | undefined>(undefined)

export function PortfolioProvider({ children }: { children: ReactNode }) {
  const [data, setData] = useState<PortfolioData>(defaultData)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [usingFallback, setUsingFallback] = useState(false)

  const refreshData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const portfolioData = await fetchPortfolioData()

      // Check if we're using fallback data
      const isFallback =
        portfolioData.projects.some((p) => p.id.startsWith("fallback-")) ||
        !portfolioData.personalInfo ||
        !portfolioData.githubStats

      setUsingFallback(isFallback)

      if (portfolioData) {
        setData({
          personalInfo: portfolioData.personalInfo || defaultData.personalInfo,
          technologies: portfolioData.technologies || defaultData.technologies,
          projects: portfolioData.projects || defaultData.projects,
          socialLinks: portfolioData.socialLinks || defaultData.socialLinks,
          githubStats: portfolioData.githubStats || defaultData.githubStats,
        })
      }
    } catch (err) {
      console.error("Error fetching portfolio data:", err)
      setError("Failed to load portfolio data. Using fallback data.")
      setUsingFallback(true)
      setData(defaultData)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    refreshData()
  }, [])

  const updatePersonalInfo = async (info: PersonalInfo) => {
    try {
      const result = await updatePersonalInfoAction(info)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      // Update the state with the new info
      setData((prev) => ({
        ...prev,
        personalInfo: {
          ...info,
          // Ensure we keep any fields that might not be in the form
          profileImage: info.profileImage || prev.personalInfo.profileImage,
        },
      }))

      // Force a refresh of the data
      await refreshData()

      return result
    } catch (err) {
      console.error("Error updating personal info:", err)
      setUsingFallback(true)
      setData((prev) => ({ ...prev, personalInfo: info }))
      throw err
    }
  }

  const updateTechnologies = async (techs: string[]) => {
    try {
      const result = await updateTechnologiesAction(techs)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      setData((prev) => ({ ...prev, technologies: techs }))
    } catch (err) {
      console.error("Error updating technologies:", err)
      setUsingFallback(true)
      setData((prev) => ({ ...prev, technologies: techs }))
      throw err
    }
  }

  const addProject = async (project: Omit<Project, "id">) => {
    try {
      const result = await addProjectAction(project)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      if (result.id) {
        const newProject = {
          ...project,
          id: result.id,
        }

        setData((prev) => ({
          ...prev,
          projects: [...prev.projects, newProject],
        }))

        return result.id
      }

      return null
    } catch (err) {
      console.error("Error adding project:", err)
      // Create a fallback ID and add the project locally
      const fallbackId = `fallback-${Date.now()}`
      const newProject = {
        ...project,
        id: fallbackId,
      }

      setUsingFallback(true)
      setData((prev) => ({
        ...prev,
        projects: [...prev.projects, newProject],
      }))

      return fallbackId
    }
  }

  const updateProject = async (project: Project) => {
    try {
      const result = await updateProjectAction(project)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      setData((prev) => ({
        ...prev,
        projects: prev.projects.map((p) => (p.id === project.id ? project : p)),
      }))
    } catch (err) {
      console.error("Error updating project:", err)
      setUsingFallback(true)
      setData((prev) => ({
        ...prev,
        projects: prev.projects.map((p) => (p.id === project.id ? project : p)),
      }))
      throw err
    }
  }

  const deleteProject = async (id: string) => {
    try {
      const result = await deleteProjectAction(id)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      setData((prev) => ({
        ...prev,
        projects: prev.projects.filter((p) => p.id !== id),
      }))
    } catch (err) {
      console.error("Error deleting project:", err)
      setUsingFallback(true)
      setData((prev) => ({
        ...prev,
        projects: prev.projects.filter((p) => p.id !== id),
      }))
      throw err
    }
  }

  const updateSocialLinks = async (links: SocialLink[]) => {
    try {
      const result = await updateSocialLinksAction(links)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      setData((prev) => ({
        ...prev,
        socialLinks: links,
      }))
    } catch (err) {
      console.error("Error updating social links:", err)
      setUsingFallback(true)
      setData((prev) => ({
        ...prev,
        socialLinks: links,
      }))
      throw err
    }
  }

  const updateGithubStats = async (stats: GithubStats) => {
    try {
      const result = await updateGithubStatsAction(stats)

      if (result.usingFallback) {
        setUsingFallback(true)
      }

      setData((prev) => ({
        ...prev,
        githubStats: stats,
      }))
    } catch (err) {
      console.error("Error updating GitHub stats:", err)
      setUsingFallback(true)
      setData((prev) => ({
        ...prev,
        githubStats: stats,
      }))
      throw err
    }
  }

  return (
    <PortfolioContext.Provider
      value={{
        data,
        isLoading,
        error,
        usingFallback,
        updatePersonalInfo,
        updateTechnologies,
        addProject,
        updateProject,
        deleteProject,
        updateSocialLinks,
        updateGithubStats,
        refreshData,
      }}
    >
      {children}
    </PortfolioContext.Provider>
  )
}

export function usePortfolio() {
  const context = useContext(PortfolioContext)
  if (context === undefined) {
    throw new Error("usePortfolio must be used within a PortfolioProvider")
  }
  return context
}
