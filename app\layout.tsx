import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { PortfolioProvider } from "@/contexts/portfolio-context"
import CustomCursor from "@/components/custom-cursor"
import BackgroundAnimation from "@/components/background-animation"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Peanut - Portfolio",
  description: "Personal portfolio website",
    generator: 'v0.dev'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <PortfolioProvider>
          <BackgroundAnimation />
          <CustomCursor />
          <div className="relative z-10">
            {children}
          </div>
        </PortfolioProvider>
      </body>
    </html>
  )
}
