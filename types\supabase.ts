export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      personal_info: {
        Row: {
          id: string
          name: string
          role: string
          bio: string
          location: string | null
          website: string | null
          email: string
          profile_image: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          role: string
          bio: string
          location?: string | null
          website?: string | null
          email: string
          profile_image?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          role?: string
          bio?: string
          location?: string | null
          website?: string | null
          email?: string
          profile_image?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      technologies: {
        Row: {
          id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          title: string
          description: string
          image_url: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          image_url: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          image_url?: string
          created_at?: string
          updated_at?: string
        }
      }
      project_tags: {
        Row: {
          id: string
          project_id: string
          tag: string
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          tag: string
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          tag?: string
          created_at?: string
        }
      }
      social_links: {
        Row: {
          id: string
          platform: string
          url: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          platform: string
          url: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          platform?: string
          url?: string
          created_at?: string
          updated_at?: string
        }
      }
      github_stats: {
        Row: {
          id: string
          stars: number
          repos: number
          followers: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          stars: number
          repos: number
          followers: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          stars?: number
          repos?: number
          followers?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
