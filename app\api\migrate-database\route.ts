import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { readFileSync } from "fs"
import { join } from "path"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()

    // Read the migration SQL file
    const migrationPath = join(process.cwd(), "database", "migrations", "001_update_schema.sql")
    let migrationSQL: string

    try {
      migrationSQL = readFileSync(migrationPath, "utf8")
    } catch (fileError) {
      // If file doesn't exist, use inline SQL
      migrationSQL = `
        -- Enable UUID extension if not exists
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Update personal_info table
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'personal_info' 
                AND column_name = 'profile_image'
            ) THEN
                ALTER TABLE personal_info ADD COLUMN profile_image TEXT;
            END IF;
        END $$;

        -- Create or update personal_info table with proper structure
        CREATE TABLE IF NOT EXISTS personal_info (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            role TEXT NOT NULL,
            bio TEXT,
            location TEXT,
            website TEXT,
            email TEXT NOT NULL,
            profile_image TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update technologies table
        CREATE TABLE IF NOT EXISTS technologies (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update projects table
        CREATE TABLE IF NOT EXISTS projects (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            description TEXT,
            image_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update project_tags table
        CREATE TABLE IF NOT EXISTS project_tags (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            tag TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(project_id, tag)
        );

        -- Create or update social_links table
        CREATE TABLE IF NOT EXISTS social_links (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            platform TEXT NOT NULL,
            url TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(platform)
        );

        -- Create or update github_stats table
        CREATE TABLE IF NOT EXISTS github_stats (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            stars INTEGER DEFAULT 0,
            repos INTEGER DEFAULT 0,
            followers INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Insert default data if tables are empty
        INSERT INTO personal_info (name, role, bio, location, website, email, profile_image)
        SELECT 
            'Peanut',
            'Creative developer currently working on Blameo',
            'I''m currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I''m currently exploring DevOps.',
            'Hanoi',
            'truongqa.com',
            '<EMAIL>',
            '/placeholder.svg?height=500&width=500'
        WHERE NOT EXISTS (SELECT 1 FROM personal_info);

        -- Insert default social links if table is empty
        INSERT INTO social_links (platform, url)
        SELECT * FROM (VALUES
            ('GitHub', 'https://github.com/truongqa'),
            ('Website', 'https://truongqa.com'),
            ('Medium', 'https://medium.com/@truongqa'),
            ('StackOverflow', 'https://stackoverflow.com/users/truongqa')
        ) AS v(platform, url)
        WHERE NOT EXISTS (SELECT 1 FROM social_links);

        -- Insert default GitHub stats if table is empty
        INSERT INTO github_stats (stars, repos, followers)
        SELECT 42, 15, 28
        WHERE NOT EXISTS (SELECT 1 FROM github_stats);
      `
    }

    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })

    if (error) {
      console.error("Migration error:", error)
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          message: "Failed to run database migration"
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Database migration completed successfully"
    })

  } catch (error) {
    console.error("Migration error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to run database migration"
      },
      { status: 500 }
    )
  }
}
