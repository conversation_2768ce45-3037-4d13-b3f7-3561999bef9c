import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { readFileSync } from "fs"
import { join } from "path"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()

    // Read the migration SQL file
    const migrationPath = join(process.cwd(), "database", "migrations", "001_update_schema.sql")
    let migrationSQL: string

    try {
      migrationSQL = readFileSync(migrationPath, "utf8")
    } catch (fileError) {
      // If file doesn't exist, use inline SQL
      migrationSQL = `
        -- Enable UUID extension if not exists
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Update personal_info table
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'personal_info' 
                AND column_name = 'profile_image'
            ) THEN
                ALTER TABLE personal_info ADD COLUMN profile_image TEXT;
            END IF;
        END $$;

        -- Create or update personal_info table with proper structure
        CREATE TABLE IF NOT EXISTS personal_info (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            role TEXT NOT NULL,
            bio TEXT,
            location TEXT,
            website TEXT,
            email TEXT NOT NULL,
            profile_image TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update technologies table
        CREATE TABLE IF NOT EXISTS technologies (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update projects table
        CREATE TABLE IF NOT EXISTS projects (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            description TEXT,
            image_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create or update project_tags table
        CREATE TABLE IF NOT EXISTS project_tags (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            tag TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(project_id, tag)
        );

        -- Create or update social_links table
        CREATE TABLE IF NOT EXISTS social_links (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            platform TEXT NOT NULL,
            url TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(platform)
        );

        -- Create or update github_stats table
        CREATE TABLE IF NOT EXISTS github_stats (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            stars INTEGER DEFAULT 0,
            repos INTEGER DEFAULT 0,
            followers INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Insert default data if tables are empty
        INSERT INTO personal_info (name, role, bio, location, website, email, profile_image)
        SELECT 
            'Peanut',
            'Creative developer currently working on Blameo',
            'I''m currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I''m currently exploring DevOps.',
            'Hanoi',
            'truongqa.com',
            '<EMAIL>',
            '/placeholder.svg?height=500&width=500'
        WHERE NOT EXISTS (SELECT 1 FROM personal_info);

        -- Insert default social links if table is empty
        INSERT INTO social_links (platform, url)
        SELECT * FROM (VALUES
            ('GitHub', 'https://github.com/truongqa'),
            ('Website', 'https://truongqa.com'),
            ('Medium', 'https://medium.com/@truongqa'),
            ('StackOverflow', 'https://stackoverflow.com/users/truongqa')
        ) AS v(platform, url)
        WHERE NOT EXISTS (SELECT 1 FROM social_links);

        -- Insert default GitHub stats if table is empty
        INSERT INTO github_stats (stars, repos, followers)
        SELECT 42, 15, 28
        WHERE NOT EXISTS (SELECT 1 FROM github_stats);
      `
    }

    // Execute migration step by step instead of using exec_sql
    const migrationSteps = [
      // Step 1: Add profile_image column if not exists
      {
        name: "Add profile_image column",
        action: async () => {
          // Check if column exists first
          const { data: columns } = await supabase
            .from('information_schema.columns')
            .select('column_name')
            .eq('table_name', 'personal_info')
            .eq('column_name', 'profile_image')

          if (!columns || columns.length === 0) {
            // Column doesn't exist, we need to add it manually through Supabase dashboard
            return { needsManualStep: true, message: "profile_image column needs to be added manually" }
          }
          return { success: true }
        }
      },

      // Step 2: Ensure personal_info has data
      {
        name: "Seed personal_info",
        action: async () => {
          const { count } = await supabase
            .from('personal_info')
            .select('*', { count: 'exact', head: true })

          if (count === 0) {
            const { error } = await supabase.from('personal_info').insert({
              name: 'Peanut',
              role: 'Creative developer currently working on Blameo',
              bio: "I'm currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I'm currently exploring DevOps.",
              location: 'Hanoi',
              website: 'truongqa.com',
              email: '<EMAIL>',
              profile_image: '/placeholder.svg?height=500&width=500'
            })
            if (error) throw error
          }
          return { success: true }
        }
      },

      // Step 3: Ensure technologies have data
      {
        name: "Seed technologies",
        action: async () => {
          const { count } = await supabase
            .from('technologies')
            .select('*', { count: 'exact', head: true })

          if (count === 0) {
            const technologies = [
              'React', 'Next', 'TypeScript', 'JavaScript', 'Node.js', 'Vue.js',
              'HTML5', 'CSS', 'Tailwindcss', 'Redux', 'GraphQL', 'AWS',
              'Firebase', 'Docker', 'Git', 'MongoDB', 'Express.js', 'Vercel',
              'Chakra', 'Figma', 'Styled-components', 'Sass', 'Bootstrap', 'Postgres'
            ]

            for (const tech of technologies) {
              const { error } = await supabase.from('technologies').insert({ name: tech })
              if (error && !error.message.includes('duplicate')) {
                console.error(`Error inserting technology ${tech}:`, error)
              }
            }
          }
          return { success: true }
        }
      },

      // Step 4: Ensure social_links have data
      {
        name: "Seed social_links",
        action: async () => {
          const { count } = await supabase
            .from('social_links')
            .select('*', { count: 'exact', head: true })

          if (count === 0) {
            const socialLinks = [
              { platform: 'GitHub', url: 'https://github.com/truongqa' },
              { platform: 'Website', url: 'https://truongqa.com' },
              { platform: 'Medium', url: 'https://medium.com/@truongqa' },
              { platform: 'StackOverflow', url: 'https://stackoverflow.com/users/truongqa' }
            ]

            for (const link of socialLinks) {
              const { error } = await supabase.from('social_links').insert(link)
              if (error && !error.message.includes('duplicate')) {
                console.error(`Error inserting social link ${link.platform}:`, error)
              }
            }
          }
          return { success: true }
        }
      },

      // Step 5: Ensure github_stats have data
      {
        name: "Seed github_stats",
        action: async () => {
          const { count } = await supabase
            .from('github_stats')
            .select('*', { count: 'exact', head: true })

          if (count === 0) {
            const { error } = await supabase.from('github_stats').insert({
              stars: 42,
              repos: 15,
              followers: 28
            })
            if (error) throw error
          }
          return { success: true }
        }
      }
    ]

    const results = []
    let hasManualSteps = false

    for (const step of migrationSteps) {
      try {
        const result = await step.action()
        results.push({
          step: step.name,
          success: result.success || false,
          needsManualStep: result.needsManualStep || false,
          message: result.message || 'Completed'
        })

        if (result.needsManualStep) {
          hasManualSteps = true
        }
      } catch (error) {
        results.push({
          step: step.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    if (hasManualSteps) {
      return NextResponse.json({
        success: true,
        message: "Migration completed with manual steps required",
        results,
        manualSteps: [
          "Please add 'profile_image TEXT' column to personal_info table in Supabase dashboard",
          "Go to Supabase Dashboard > Table Editor > personal_info > Add Column",
          "Column name: profile_image, Type: text, Nullable: true"
        ]
      })
    }

    return NextResponse.json({
      success: true,
      message: "Database migration completed successfully",
      results
    })

  } catch (error) {
    console.error("Migration error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to run database migration"
      },
      { status: 500 }
    )
  }
}
