"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Check, AlertTriangle, RefreshCw, Database, HardDrive } from "lucide-react"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

export default function StorageDebugPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [storageStatus, setStorageStatus] = useState<any>(null)
  const [bucketStatus, setBucketStatus] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isCreatingBucket, setIsCreatingBucket] = useState(false)
  const [bucketName, setBucketName] = useState("portfolio")
  const [testUploadResult, setTestUploadResult] = useState<any>(null)
  const [isTestingUpload, setIsTestingUpload] = useState(false)

  // Kiểm tra trạng thái Storage khi trang được tải
  useEffect(() => {
    checkStorageStatus()
  }, [])

  // Kiểm tra trạng thái Storage
  const checkStorageStatus = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/storage-debug", {
        method: "GET",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể kiểm tra trạng thái Storage")
      }

      const data = await response.json()
      setStorageStatus(data)

      // Kiểm tra trạng thái bucket
      await checkBucketStatus(bucketName)
    } catch (err) {
      console.error("Lỗi khi kiểm tra trạng thái Storage:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsLoading(false)
    }
  }

  // Kiểm tra trạng thái bucket
  const checkBucketStatus = async (name: string) => {
    try {
      const response = await fetch("/api/check-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName: name }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể kiểm tra trạng thái bucket")
      }

      const data = await response.json()
      setBucketStatus(data)
    } catch (err) {
      console.error(`Lỗi khi kiểm tra trạng thái bucket '${name}':`, err)
      setBucketStatus({
        exists: false,
        error: err instanceof Error ? err.message : "Lỗi không xác định",
      })
    }
  }

  // Tạo bucket mới
  const createBucket = async () => {
    setIsCreatingBucket(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/create-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể tạo bucket")
      }

      const data = await response.json()
      setSuccess(`Đã tạo bucket '${bucketName}' thành công`)

      // Kiểm tra lại trạng thái bucket
      await checkBucketStatus(bucketName)
    } catch (err) {
      console.error(`Lỗi khi tạo bucket '${bucketName}':`, err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsCreatingBucket(false)
    }
  }

  // Thử upload file test
  const testUpload = async () => {
    setIsTestingUpload(true)
    setError(null)
    setSuccess(null)
    setTestUploadResult(null)

    try {
      const response = await fetch("/api/test-upload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể upload file test")
      }

      const data = await response.json()
      setTestUploadResult(data)
      setSuccess(`Đã upload file test thành công: ${data.filePath}`)

      // Kiểm tra lại trạng thái bucket
      await checkBucketStatus(bucketName)
    } catch (err) {
      console.error(`Lỗi khi upload file test vào bucket '${bucketName}':`, err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
      setTestUploadResult({ success: false, error: err instanceof Error ? err.message : "Lỗi không xác định" })
    } finally {
      setIsTestingUpload(false)
    }
  }

  // Sửa lỗi Storage
  const fixStorageIssues = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/fix-storage", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể sửa lỗi Storage")
      }

      const data = await response.json()
      setSuccess(data.message || "Đã sửa lỗi Storage thành công")

      // Kiểm tra lại trạng thái
      await checkStorageStatus()
    } catch (err) {
      console.error("Lỗi khi sửa lỗi Storage:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Kiểm tra và sửa lỗi Storage</h1>
          <p className="text-gray-500 mt-2">Kiểm tra và sửa lỗi liên quan đến Supabase Storage</p>
        </div>
        <Button onClick={checkStorageStatus} variant="outline" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang kiểm tra...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" /> Làm mới
            </>
          )}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="status">
        <TabsList>
          <TabsTrigger value="status">Trạng thái</TabsTrigger>
          <TabsTrigger value="bucket">Quản lý Bucket</TabsTrigger>
          <TabsTrigger value="test">Kiểm tra Upload</TabsTrigger>
          <TabsTrigger value="fix">Sửa lỗi</TabsTrigger>
        </TabsList>

        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Trạng thái Supabase Storage</CardTitle>
              <CardDescription>Thông tin về trạng thái kết nối và quyền truy cập Storage</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : storageStatus ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-md">
                      <h3 className="font-medium mb-2 flex items-center">
                        <Database className="mr-2 h-4 w-4" /> Kết nối Supabase
                      </h3>
                      <div className="flex items-center">
                        <div
                          className={`w-3 h-3 rounded-full mr-2 ${
                            storageStatus.connection ? "bg-green-500" : "bg-red-500"
                          }`}
                        ></div>
                        <span>{storageStatus.connection ? "Kết nối thành công" : "Không thể kết nối"}</span>
                      </div>
                    </div>
                    <div className="p-4 border rounded-md">
                      <h3 className="font-medium mb-2 flex items-center">
                        <HardDrive className="mr-2 h-4 w-4" /> Storage API
                      </h3>
                      <div className="flex items-center">
                        <div
                          className={`w-3 h-3 rounded-full mr-2 ${
                            storageStatus.storageEnabled ? "bg-green-500" : "bg-red-500"
                          }`}
                        ></div>
                        <span>{storageStatus.storageEnabled ? "Storage khả dụng" : "Storage không khả dụng"}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <h3 className="font-medium mb-2">Thông tin chi tiết:</h3>
                    <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60">
                      {JSON.stringify(storageStatus, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="text-center p-4 text-gray-500">Không có thông tin trạng thái</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bucket">
          <Card>
            <CardHeader>
              <CardTitle>Quản lý Bucket</CardTitle>
              <CardDescription>Kiểm tra và tạo bucket trong Supabase Storage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-end gap-4">
                  <div className="flex-1">
                    <Label htmlFor="bucket-name">Tên Bucket</Label>
                    <Input
                      id="bucket-name"
                      value={bucketName}
                      onChange={(e) => setBucketName(e.target.value)}
                      placeholder="Nhập tên bucket"
                    />
                  </div>
                  <Button onClick={() => checkBucketStatus(bucketName)} variant="outline" disabled={isLoading}>
                    Kiểm tra
                  </Button>
                  <Button onClick={createBucket} disabled={isCreatingBucket}>
                    {isCreatingBucket ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang tạo...
                      </>
                    ) : (
                      "Tạo Bucket"
                    )}
                  </Button>
                </div>

                {bucketStatus && (
                  <div className="mt-4 p-4 border rounded-md">
                    <h3 className="font-medium mb-2">Trạng thái Bucket:</h3>
                    <div className="flex items-center mb-4">
                      <div
                        className={`w-3 h-3 rounded-full mr-2 ${bucketStatus.exists ? "bg-green-500" : "bg-red-500"}`}
                      ></div>
                      <span>
                        {bucketStatus.exists
                          ? `Bucket '${bucketName}' tồn tại`
                          : `Bucket '${bucketName}' không tồn tại`}
                      </span>
                    </div>
                    <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-40">
                      {JSON.stringify(bucketStatus, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test">
          <Card>
            <CardHeader>
              <CardTitle>Kiểm tra Upload</CardTitle>
              <CardDescription>Thử upload một file test để kiểm tra quyền truy cập</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-end gap-4">
                  <div className="flex-1">
                    <Label htmlFor="test-bucket-name">Bucket để test</Label>
                    <Input
                      id="test-bucket-name"
                      value={bucketName}
                      onChange={(e) => setBucketName(e.target.value)}
                      placeholder="Nhập tên bucket"
                    />
                  </div>
                  <Button onClick={testUpload} disabled={isTestingUpload}>
                    {isTestingUpload ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang test...
                      </>
                    ) : (
                      "Test Upload"
                    )}
                  </Button>
                </div>

                {testUploadResult && (
                  <div className="mt-4 p-4 border rounded-md">
                    <h3 className="font-medium mb-2">Kết quả Test:</h3>
                    <div className="flex items-center mb-4">
                      <div
                        className={`w-3 h-3 rounded-full mr-2 ${
                          testUploadResult.success ? "bg-green-500" : "bg-red-500"
                        }`}
                      ></div>
                      <span>{testUploadResult.success ? "Upload thành công" : "Upload thất bại"}</span>
                    </div>
                    <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-40">
                      {JSON.stringify(testUploadResult, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fix">
          <Card>
            <CardHeader>
              <CardTitle>Sửa lỗi Storage</CardTitle>
              <CardDescription>Thực hiện các bước để sửa lỗi liên quan đến Storage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert className="bg-yellow-50 border-yellow-200">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    Chức năng này sẽ thực hiện các bước sau:
                    <ul className="list-disc pl-5 mt-2">
                      <li>Kiểm tra quyền truy cập Storage API</li>
                      <li>Tạo bucket nếu chưa tồn tại</li>
                      <li>Cập nhật quyền cho bucket</li>
                      <li>Kiểm tra khả năng upload file</li>
                    </ul>
                  </AlertDescription>
                </Alert>

                <Button onClick={fixStorageIssues} disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang sửa lỗi...
                    </>
                  ) : (
                    "Sửa lỗi Storage"
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
