"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import ImageUpload from "@/components/image-upload"
import DirectImageUpload from "@/components/direct-image-upload"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function TestUploadPage() {
  const [imageUrl, setImageUrl] = useState("")
  const [directImageUrl, setDirectImageUrl] = useState("")

  const handleImageUploaded = (url: string) => {
    setImageUrl(url)
    if (url) {
      toast({
        title: "Image uploaded successfully",
        description: "The image has been uploaded to Supabase Storage.",
      })
    }
  }

  const handleDirectImageUploaded = (url: string) => {
    setDirectImageUrl(url)
    if (url) {
      toast({
        title: "Image uploaded successfully (Direct)",
        description: "The image has been uploaded to Supabase Storage using direct upload.",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Test Image Upload</h1>
        <p className="text-gray-500 mt-2">Test the image upload functionality</p>
      </div>

      <Tabs defaultValue="standard">
        <TabsList>
          <TabsTrigger value="standard">Standard Upload</TabsTrigger>
          <TabsTrigger value="direct">Direct Upload</TabsTrigger>
        </TabsList>

        <TabsContent value="standard">
          <Card>
            <CardHeader>
              <CardTitle>Standard Upload Test</CardTitle>
              <CardDescription>Uses API route to ensure bucket exists before upload</CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUpload onImageUploaded={handleImageUploaded} currentImageUrl={imageUrl} folderPath="test" />

              {imageUrl && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Image URL:</h3>
                  <code className="block p-3 bg-gray-100 rounded-md overflow-auto text-sm">{imageUrl}</code>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                onClick={() => {
                  if (imageUrl) {
                    navigator.clipboard.writeText(imageUrl)
                    toast({
                      title: "URL copied",
                      description: "The image URL has been copied to your clipboard.",
                    })
                  }
                }}
                disabled={!imageUrl}
              >
                Copy URL
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="direct">
          <Card>
            <CardHeader>
              <CardTitle>Direct Upload Test</CardTitle>
              <CardDescription>Attempts to upload directly without checking bucket</CardDescription>
            </CardHeader>
            <CardContent>
              <DirectImageUpload
                onImageUploaded={handleDirectImageUploaded}
                currentImageUrl={directImageUrl}
                folderPath="test-direct"
              />

              {directImageUrl && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Image URL:</h3>
                  <code className="block p-3 bg-gray-100 rounded-md overflow-auto text-sm">{directImageUrl}</code>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                onClick={() => {
                  if (directImageUrl) {
                    navigator.clipboard.writeText(directImageUrl)
                    toast({
                      title: "URL copied",
                      description: "The image URL has been copied to your clipboard.",
                    })
                  }
                }}
                disabled={!directImageUrl}
              >
                Copy URL
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <Toaster />
    </div>
  )
}
