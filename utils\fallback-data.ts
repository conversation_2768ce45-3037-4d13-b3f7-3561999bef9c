import type { PersonalInfo, Project, SocialLink, GithubStats } from "@/actions/portfolio-actions"

export interface PortfolioFallbackData {
  personalInfo: PersonalInfo
  technologies: string[]
  projects: Project[]
  socialLinks: SocialLink[]
  githubStats: GithubStats
}

export function generateFallbackData(): PortfolioFallbackData {
  return {
    personalInfo: {
      name: "<PERSON><PERSON>",
      role: "Creative developer currently working on Blameo",
      bio: "I'm currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I'm currently exploring DevOps.",
      location: "Hanoi",
      website: "truongqa.com",
      email: "<EMAIL>",
      profileImage: "/placeholder.svg?height=500&width=500", // Add this line
    },
    technologies: [
      "React",
      "Next",
      "TypeScript",
      "JavaScript",
      "Node.js",
      "Vue.js",
      "HTML5",
      "CSS",
      "Tailwindcss",
      "Redux",
      "GraphQL",
      "AWS",
      "Firebase",
      "Docker",
      "Git",
      "MongoDB",
      "Express.js",
      "Vercel",
      "Chakra",
      "Figma",
      "Styled-components",
      "Sass",
      "Bootstrap",
      "Postgres",
    ],
    projects: [
      {
        id: "fallback-1",
        title: "E-commerce Platform",
        description: "A modern e-commerce platform with a unique shopping experience",
        tags: ["Next.js", "Tailwind", "Stripe"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
      {
        id: "fallback-2",
        title: "Portfolio Website",
        description: "A creative portfolio for a digital artist with interactive elements",
        tags: ["React", "Framer Motion", "Three.js"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
      {
        id: "fallback-3",
        title: "Mobile App",
        description: "A lifestyle app with a clean, minimalist interface",
        tags: ["React Native", "Firebase", "UI/UX"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
      {
        id: "fallback-4",
        title: "Dashboard UI",
        description: "An analytics dashboard with data visualization",
        tags: ["TypeScript", "D3.js", "Tailwind"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
      {
        id: "fallback-5",
        title: "Blog Platform",
        description: "A content management system with a modern interface",
        tags: ["Next.js", "Prisma", "PostgreSQL"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
      {
        id: "fallback-6",
        title: "Landing Page",
        description: "A conversion-focused landing page with animations",
        tags: ["HTML/CSS", "JavaScript", "GSAP"],
        imageUrl: "/placeholder.svg?height=400&width=600",
      },
    ],
    socialLinks: [
      {
        id: "fallback-github",
        platform: "GitHub",
        url: "https://github.com/truongnat",
      },
      {
        id: "fallback-facebook",
        platform: "Facebook",
        url: "https://facebook.com",
      },
      {
        id: "fallback-medium",
        platform: "Medium",
        url: "https://medium.com",
      },
      {
        id: "fallback-stackoverflow",
        platform: "StackOverflow",
        url: "https://stackoverflow.com",
      },
    ],
    githubStats: {
      stars: 31,
      repos: 16,
      followers: 13,
    },
  }
}
