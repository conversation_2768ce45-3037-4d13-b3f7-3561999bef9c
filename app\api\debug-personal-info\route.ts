import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = createServiceSupabaseClient()
    
    // Get personal info from database
    const { data: personalInfo, error: fetchError } = await supabase
      .from('personal_info')
      .select('*')
      .limit(1)
      .single()

    if (fetchError) {
      return NextResponse.json({
        success: false,
        error: fetchError.message,
        message: "Failed to fetch personal info from database",
        databaseData: null,
        hasData: false
      })
    }

    // Check if profile_image field exists and has value
    const hasProfileImage = personalInfo && 'profile_image' in personalInfo
    const profileImageValue = personalInfo?.profile_image

    // Get table structure
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'personal_info')
      .order('ordinal_position')

    return NextResponse.json({
      success: true,
      message: "Personal info debug data retrieved",
      databaseData: personalInfo,
      hasData: !!personalInfo,
      profileImageInfo: {
        fieldExists: hasProfileImage,
        value: profileImageValue,
        hasValue: !!profileImageValue,
        valueType: typeof profileImageValue
      },
      tableStructure: columns || [],
      structureError: columnsError?.message,
      recommendations: [
        !personalInfo && "No personal info data found - run database migration",
        !hasProfileImage && "profile_image field missing - add column to table",
        !profileImageValue && "profile_image field is empty - upload an image",
        profileImageValue && !profileImageValue.startsWith('http') && "profile_image value might be invalid URL"
      ].filter(Boolean)
    })

  } catch (error) {
    console.error("Debug personal info error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to debug personal info"
      },
      { status: 500 }
    )
  }
}
