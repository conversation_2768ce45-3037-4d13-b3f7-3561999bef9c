import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()
    const results = []

    // 1. Seed personal_info if empty
    try {
      const { count: personalCount } = await supabase
        .from('personal_info')
        .select('*', { count: 'exact', head: true })

      if (personalCount === 0) {
        const { error } = await supabase.from('personal_info').insert({
          name: 'Peanut',
          role: 'Creative developer currently working on Blameo',
          bio: "I'm currently working on Blameo and looking for freelance job opportunities. With a passion for development and learning, I'm currently exploring DevOps.",
          location: 'Hanoi',
          website: 'truongqa.com',
          email: '<EMAIL>'
          // Note: profile_image will be added if column exists
        })
        
        if (error) {
          results.push({ table: 'personal_info', success: false, error: error.message })
        } else {
          results.push({ table: 'personal_info', success: true, message: 'Data inserted' })
        }
      } else {
        results.push({ table: 'personal_info', success: true, message: 'Already has data' })
      }
    } catch (error) {
      results.push({ table: 'personal_info', success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    }

    // 2. Seed technologies if empty
    try {
      const { count: techCount } = await supabase
        .from('technologies')
        .select('*', { count: 'exact', head: true })

      if (techCount === 0) {
        const technologies = [
          'React', 'Next', 'TypeScript', 'JavaScript', 'Node.js', 'Vue.js',
          'HTML5', 'CSS', 'Tailwindcss', 'Redux', 'GraphQL', 'AWS',
          'Firebase', 'Docker', 'Git', 'MongoDB', 'Express.js', 'Vercel',
          'Chakra', 'Figma', 'Styled-components', 'Sass', 'Bootstrap', 'Postgres'
        ]
        
        let successCount = 0
        for (const tech of technologies) {
          const { error } = await supabase.from('technologies').insert({ name: tech })
          if (!error) successCount++
        }
        
        results.push({ table: 'technologies', success: true, message: `Inserted ${successCount}/${technologies.length} technologies` })
      } else {
        results.push({ table: 'technologies', success: true, message: 'Already has data' })
      }
    } catch (error) {
      results.push({ table: 'technologies', success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    }

    // 3. Seed social_links if empty
    try {
      const { count: socialCount } = await supabase
        .from('social_links')
        .select('*', { count: 'exact', head: true })

      if (socialCount === 0) {
        const socialLinks = [
          { platform: 'GitHub', url: 'https://github.com/truongqa' },
          { platform: 'Website', url: 'https://truongqa.com' },
          { platform: 'Medium', url: 'https://medium.com/@truongqa' },
          { platform: 'StackOverflow', url: 'https://stackoverflow.com/users/truongqa' }
        ]
        
        let successCount = 0
        for (const link of socialLinks) {
          const { error } = await supabase.from('social_links').insert(link)
          if (!error) successCount++
        }
        
        results.push({ table: 'social_links', success: true, message: `Inserted ${successCount}/${socialLinks.length} social links` })
      } else {
        results.push({ table: 'social_links', success: true, message: 'Already has data' })
      }
    } catch (error) {
      results.push({ table: 'social_links', success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    }

    // 4. Seed github_stats if empty
    try {
      const { count: githubCount } = await supabase
        .from('github_stats')
        .select('*', { count: 'exact', head: true })

      if (githubCount === 0) {
        const { error } = await supabase.from('github_stats').insert({
          stars: 42,
          repos: 15,
          followers: 28
        })
        
        if (error) {
          results.push({ table: 'github_stats', success: false, error: error.message })
        } else {
          results.push({ table: 'github_stats', success: true, message: 'Data inserted' })
        }
      } else {
        results.push({ table: 'github_stats', success: true, message: 'Already has data' })
      }
    } catch (error) {
      results.push({ table: 'github_stats', success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    }

    // 5. Seed sample projects if empty
    try {
      const { count: projectCount } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })

      if (projectCount === 0) {
        const projects = [
          {
            title: "E-commerce Platform",
            description: "A modern e-commerce platform with a unique shopping experience",
            image_url: "/placeholder.svg?height=400&width=600",
            tags: ["Next.js", "Tailwind", "Stripe"]
          },
          {
            title: "Portfolio Website",
            description: "A creative portfolio for a digital artist with interactive elements",
            image_url: "/placeholder.svg?height=400&width=600",
            tags: ["React", "Framer Motion", "Three.js"]
          },
          {
            title: "Task Management App",
            description: "A collaborative task management application with real-time updates",
            image_url: "/placeholder.svg?height=400&width=600",
            tags: ["Vue.js", "Node.js", "Socket.io"]
          }
        ]
        
        let successCount = 0
        for (const project of projects) {
          const { data: newProject, error } = await supabase
            .from('projects')
            .insert({
              title: project.title,
              description: project.description,
              image_url: project.image_url
            })
            .select('id')
            .single()

          if (!error && newProject) {
            successCount++
            
            // Add tags for this project
            for (const tag of project.tags) {
              await supabase.from('project_tags').insert({
                project_id: newProject.id,
                tag
              })
            }
          }
        }
        
        results.push({ table: 'projects', success: true, message: `Inserted ${successCount}/${projects.length} projects with tags` })
      } else {
        results.push({ table: 'projects', success: true, message: 'Already has data' })
      }
    } catch (error) {
      results.push({ table: 'projects', success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    }

    const successfulTables = results.filter(r => r.success).length
    const totalTables = results.length

    return NextResponse.json({
      success: true,
      message: `Seeding completed: ${successfulTables}/${totalTables} tables processed successfully`,
      results,
      summary: {
        successful: successfulTables,
        total: totalTables,
        allSuccess: successfulTables === totalTables
      }
    })

  } catch (error) {
    console.error("Simple seed error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to seed database"
      },
      { status: 500 }
    )
  }
}
