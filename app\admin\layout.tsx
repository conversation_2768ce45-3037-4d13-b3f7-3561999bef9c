"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { AuthProvider } from "@/contexts/auth-context"
import { PortfolioProvider } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import {
  User,
  Code,
  Github,
  LogOut,
  Settings,
  Share2,
  LayoutDashboard,
  Layers,
  Database,
  Upload,
  HardDrive,
  DatabaseIcon as Database2,
  Image as ImageIcon,
} from "lucide-react"

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <PortfolioProvider>
        <AdminLayoutContent>{children}</AdminLayoutContent>
      </PortfolioProvider>
    </AuthProvider>
  )
}

function AdminLayoutContent({ children }: { children: React.ReactNode }) {
  const { user, logout, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!isLoading && !user && pathname !== "/admin/login") {
      router.push("/admin/login")
    }
  }, [user, isLoading, router, pathname])

  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>
  }

  if (!user && pathname !== "/admin/login") {
    return null
  }

  if (pathname === "/admin/login") {
    return children
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r">
            <div className="px-4 pb-2 flex items-center">
              <h1 className="text-xl font-semibold">Portfolio Admin</h1>
            </div>
            <div className="px-4 py-2">
              <p className="text-sm text-gray-500">Logged in as {user?.email}</p>
              <div className="flex items-center gap-2 mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-600">Authenticated</span>
              </div>
            </div>
            <div className="mt-5 flex-1 flex flex-col">
              <nav className="flex-1 px-2 space-y-1">
                <Link
                  href="/admin/dashboard"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/dashboard"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <LayoutDashboard className="mr-3 h-5 w-5" />
                  Dashboard
                </Link>
                <Link
                  href="/admin/personal-info"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/personal-info"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <User className="mr-3 h-5 w-5" />
                  Personal Info
                </Link>
                <Link
                  href="/admin/technologies"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/technologies"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Layers className="mr-3 h-5 w-5" />
                  Technologies
                </Link>
                <Link
                  href="/admin/projects"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/projects"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Code className="mr-3 h-5 w-5" />
                  Projects
                </Link>
                <Link
                  href="/admin/social-links"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/social-links"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Share2 className="mr-3 h-5 w-5" />
                  Social Links
                </Link>
                <Link
                  href="/admin/github-stats"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/github-stats"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Github className="mr-3 h-5 w-5" />
                  GitHub Stats
                </Link>
                <Link
                  href="/admin/test-upload"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/test-upload"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Upload className="mr-3 h-5 w-5" />
                  Test Upload
                </Link>
                <Link
                  href="/admin/storage-debug"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/storage-debug"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <HardDrive className="mr-3 h-5 w-5" />
                  Storage Debug
                </Link>
                <Link
                  href="/admin/supabase-config"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/supabase-config"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Database2 className="mr-3 h-5 w-5" />
                  Supabase Config
                </Link>
                <Link
                  href="/admin/settings"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/settings"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Settings className="mr-3 h-5 w-5" />
                  Settings
                </Link>
                <Link
                  href="/admin/database-migration"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/database-migration"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Database className="mr-3 h-5 w-5" />
                  Database Setup
                </Link>
                <Link
                  href="/admin/table-status"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/table-status"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Database className="mr-3 h-5 w-5" />
                  Table Status
                </Link>
                <Link
                  href="/admin/auth-debug"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/auth-debug"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <User className="mr-3 h-5 w-5" />
                  Auth Debug
                </Link>
                <Link
                  href="/admin/test-image-display"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/test-image-display"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <ImageIcon className="mr-3 h-5 w-5" />
                  Test Images
                </Link>
                <Link
                  href="/admin/storage-setup"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/storage-setup"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <HardDrive className="mr-3 h-5 w-5" />
                  Storage Setup
                </Link>
                <Link
                  href="/admin/manual-setup"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/manual-setup"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Settings className="mr-3 h-5 w-5" />
                  Manual Setup
                </Link>
                {/* Add the seed database link in the sidebar navigation */}
                <Link
                  href="/admin/seed-database"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                    pathname === "/admin/seed-database"
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <Database2 className="mr-3 h-5 w-5" />
                  Seed Database
                </Link>
              </nav>
            </div>
            <div className="p-4">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => {
                  logout()
                  router.push("/admin/login")
                }}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          <main className="flex-1 relative overflow-y-auto focus:outline-none p-6">{children}</main>
        </div>
      </div>
    </div>
  )
}
