"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface DebugInfoProps {
  data: any
  title?: string
}

export default function DebugInfo({ data, title = "Debug Information" }: DebugInfoProps) {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <div className="mt-4">
      <Button variant="outline" size="sm" onClick={() => setIsVisible(!isVisible)}>
        {isVisible ? "Hide" : "Show"} Debug Info
      </Button>

      {isVisible && (
        <Card className="mt-2">
          <CardHeader>
            <CardTitle className="text-sm">{title}</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs overflow-auto max-h-96 bg-gray-50 p-2 rounded-md">
              {JSON.stringify(data, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
