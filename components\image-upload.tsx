"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, ImageIcon, Loader2, AlertCircle, RefreshCw } from "lucide-react"
import { ensureClientBucket } from "@/utils/supabase/client-storage"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ImageUploadProps {
  onImageUploaded: (url: string) => void
  currentImageUrl?: string
  bucketName?: string
  folderPath?: string
  className?: string
}

export default function ImageUpload({
  onImageUploaded,
  currentImageUrl,
  bucketName = "general", // Mặc định là bucket general
  folderPath = "images",
  className,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null)
  const [error, setError] = useState<string | null>(null)
  const [isBucketChecked, setIsBucketChecked] = useState(false)
  const [isBucketReady, setIsBucketReady] = useState(false)
  const [isFixingSchema, setIsFixingSchema] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Sync previewUrl với currentImageUrl khi prop thay đổi
  useEffect(() => {
    setPreviewUrl(currentImageUrl || null)
  }, [currentImageUrl])

  // Kiểm tra và sửa schema
  const fixSchema = async () => {
    setIsFixingSchema(true)
    setError(null)

    try {
      const response = await fetch("/api/fix-schema", {
        method: "POST",
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Không thể sửa schema")
      }

      // Kiểm tra bucket sau khi sửa schema
      checkBucket()

      return true
    } catch (err) {
      console.error("Lỗi khi sửa schema:", err)
      setError(`Lỗi khi sửa schema: ${err instanceof Error ? err.message : "Lỗi không xác định"}`)
      return false
    } finally {
      setIsFixingSchema(false)
    }
  }

  // Kiểm tra bucket
  const checkBucket = async () => {
    try {
      setIsBucketChecked(false)
      setError(null)

      // Gọi API để kiểm tra bucket
      const response = await fetch("/api/check-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Lỗi khi kiểm tra bucket:", errorData)
        setIsBucketReady(false)
        setIsBucketChecked(true)
        setError(`Không thể kiểm tra bucket: ${errorData.error || "Lỗi không xác định"}`)
        return
      }

      const data = await response.json()
      setIsBucketReady(data.exists)
      setIsBucketChecked(true)

      if (!data.exists) {
        console.warn(`Bucket '${bucketName}' không tồn tại, sẽ thử tạo khi upload`)
        setError(`Storage bucket "${bucketName}" không khả dụng. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.`)
      }
    } catch (err) {
      console.error("Lỗi khi kiểm tra bucket:", err)
      setError("Không thể kiểm tra khả năng lưu trữ. Vui lòng thử lại sau.")
      setIsBucketReady(false)
      setIsBucketChecked(true)
    }
  }

  // Kiểm tra bucket khi component mount hoặc khi bucketName thay đổi
  useEffect(() => {
    checkBucket()
  }, [bucketName])

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith("image/")) {
      setError("Vui lòng chọn file hình ảnh")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError("Kích thước hình ảnh phải nhỏ hơn 5MB")
      return
    }

    setError(null)
    setIsUploading(true)

    try {
      // Tạo URL xem trước
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)

      // Nếu bucket chưa sẵn sàng, thử sửa lỗi bucket trước
      if (!isBucketReady) {
        console.log(`Bucket '${bucketName}' chưa sẵn sàng, đang thử sửa lỗi...`)

        try {
          const fixResponse = await fetch("/api/fix-bucket", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ bucketName }),
          })

          if (!fixResponse.ok) {
            const errorData = await fixResponse.json()
            console.error("Không thể sửa lỗi bucket:", errorData)
            throw new Error(`Storage bucket "${bucketName}" không khả dụng. Vui lòng thử lại sau.`)
          }

          console.log(`Đã sửa lỗi bucket '${bucketName}' thành công`)
          // Cập nhật trạng thái bucket
          setIsBucketReady(true)
        } catch (fixErr) {
          console.error("Lỗi khi sửa bucket:", fixErr)
          throw new Error(`Không thể sửa lỗi bucket: ${fixErr instanceof Error ? fixErr.message : "Lỗi không xác định"}`)
        }
      }

      // Upload qua API route
      console.log(`Đang upload file vào bucket '${bucketName}'...`)
      const formData = new FormData()
      formData.append("file", file)
      formData.append("bucketName", bucketName)
      formData.append("folderPath", folderPath)

      const response = await fetch("/api/upload-file", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Lỗi khi upload file:", errorData)
        throw new Error(errorData.error || "Upload thất bại")
      }

      const result = await response.json()
      console.log("Upload thành công:", result.publicUrl)

      // Gọi callback với URL mới
      onImageUploaded(result.publicUrl)
    } catch (err) {
      console.error("Lỗi khi upload hình ảnh:", err)
      setError(err instanceof Error ? err.message : "Không thể upload hình ảnh. Vui lòng thử lại.")
      // Quay lại hình ảnh trước đó nếu có
      setPreviewUrl(currentImageUrl || null)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onImageUploaded("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  if (!isBucketChecked) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center p-4 border border-gray-200 rounded-md">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400 mr-2" />
          <span>Đang chuẩn bị chức năng upload...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label htmlFor="image-upload">Upload Hình ảnh ({bucketName})</Label>
          <div className="flex items-center gap-2 text-xs">
            <span className="text-gray-500">Bucket:</span>
            <div className="flex items-center">
              <div
                className={`w-2 h-2 rounded-full mr-1 ${
                  !isBucketChecked ? "bg-yellow-500" : isBucketReady ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <span className={!isBucketChecked ? "text-yellow-600" : isBucketReady ? "text-green-600" : "text-red-600"}>
                {!isBucketChecked ? "Đang kiểm tra..." : isBucketReady ? "Sẵn sàng" : "Không sẵn sàng"}
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={checkBucket}
              disabled={!isBucketChecked || isFixingSchema}
              title="Làm mới trạng thái bucket"
            >
              <RefreshCw className={`h-3 w-3 ${!isBucketChecked ? "animate-spin" : ""}`} />
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Input
            ref={fileInputRef}
            id="image-upload"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            disabled={isUploading}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang upload...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                {previewUrl ? "Thay đổi ảnh" : "Chọn hình ảnh"}
              </>
            )}
          </Button>
          {!isBucketReady && isBucketChecked && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                fetch("/api/fix-bucket", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({ bucketName }),
                })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    checkBucket();
                  }
                })
                .catch(err => console.error("Lỗi khi sửa bucket:", err));
              }}
              className="ml-2 text-xs"
            >
              <AlertCircle className="mr-1 h-3 w-3" /> Sửa lỗi bucket
            </Button>
          )}
        </div>

        {error && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{error}</span>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={fixSchema}
                disabled={isFixingSchema}
                className="ml-2"
              >
                {isFixingSchema ? (
                  <>
                    <Loader2 className="mr-2 h-3 w-3 animate-spin" /> Đang sửa...
                  </>
                ) : (
                  "Sửa lỗi"
                )}
              </Button>
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Image Preview */}
      {previewUrl ? (
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">
              {currentImageUrl === previewUrl ? "Ảnh hiện tại:" : "Ảnh xem trước:"}
            </Label>
            <div className="flex items-center gap-2">
              {currentImageUrl === previewUrl && (
                <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                  ✓ Đã lưu
                </span>
              )}
              {currentImageUrl !== previewUrl && (
                <span className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                  ⚠ Chưa lưu
                </span>
              )}
            </div>
          </div>
          <div className="relative aspect-square w-full max-w-sm overflow-hidden rounded-lg border border-gray-200 bg-gray-50">
            <img
              src={previewUrl}
              alt={currentImageUrl === previewUrl ? "Current image" : "Preview"}
              className="h-full w-full object-cover"
              onError={(e) => {
                console.error("Image load error:", previewUrl)
                e.currentTarget.src = "/placeholder.svg?height=300&width=400"
              }}
            />
            <div className="absolute top-2 right-2">
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="h-8 w-8 bg-red-500/80 hover:bg-red-500"
                onClick={handleRemoveImage}
                title="Xóa ảnh"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            <p>URL: <span className="font-mono break-all">{previewUrl}</span></p>
          </div>
        </div>
      ) : (
        <div className="mt-4 flex aspect-square w-full max-w-sm items-center justify-center rounded-lg border border-dashed border-gray-300 bg-gray-50">
          <div className="flex flex-col items-center gap-2 text-gray-500">
            <ImageIcon className="h-12 w-12" />
            <div className="text-center">
              <p className="text-sm font-medium">Chưa có ảnh</p>
              <p className="text-xs">Nhấn "Chọn hình ảnh" để upload</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
