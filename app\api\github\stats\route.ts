import { NextResponse } from "next/server"
import { githubAPI, type GitHubStats } from "@/lib/github-api"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const username = searchParams.get("username")

    if (!username) {
      return NextResponse.json(
        { error: "Username parameter is required" },
        { status: 400 }
      )
    }

    // Validate username format
    if (!/^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i.test(username)) {
      return NextResponse.json(
        { error: "Invalid GitHub username format" },
        { status: 400 }
      )
    }

    console.log(`Fetching GitHub stats for: ${username}`)

    // Fetch GitHub stats
    const stats = await githubAPI.getUserStats(username)

    // Get rate limit info
    const rateLimit = await githubAPI.getRateLimit()

    return NextResponse.json({
      success: true,
      data: stats,
      meta: {
        username,
        fetchedAt: new Date().toISOString(),
        rateLimit: {
          limit: rateLimit.rate.limit,
          remaining: rateLimit.rate.remaining,
          reset: new Date(rateLimit.rate.reset * 1000).toISOString()
        }
      }
    })

  } catch (error) {
    console.error("GitHub API error:", error)

    // Handle specific GitHub API errors
    if (error instanceof Error) {
      if (error.message.includes("404")) {
        return NextResponse.json(
          { 
            error: "GitHub user not found",
            details: "The specified username does not exist on GitHub"
          },
          { status: 404 }
        )
      }

      if (error.message.includes("403")) {
        return NextResponse.json(
          { 
            error: "GitHub API rate limit exceeded",
            details: "Please try again later or configure a GitHub token"
          },
          { status: 429 }
        )
      }

      if (error.message.includes("401")) {
        return NextResponse.json(
          { 
            error: "GitHub API authentication failed",
            details: "Invalid or expired GitHub token"
          },
          { status: 401 }
        )
      }
    }

    return NextResponse.json(
      { 
        error: "Failed to fetch GitHub data",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}

// POST endpoint to test GitHub connection
export async function POST(request: Request) {
  try {
    const { username } = await request.json()

    if (!username) {
      return NextResponse.json(
        { error: "Username is required" },
        { status: 400 }
      )
    }

    // Test connection by fetching basic user info
    const user = await githubAPI.getUser(username)
    const rateLimit = await githubAPI.getRateLimit()

    return NextResponse.json({
      success: true,
      message: "GitHub connection successful",
      data: {
        username: user.login,
        name: user.name,
        publicRepos: user.public_repos,
        followers: user.followers
      },
      rateLimit: {
        limit: rateLimit.rate.limit,
        remaining: rateLimit.rate.remaining,
        reset: new Date(rateLimit.rate.reset * 1000).toISOString()
      }
    })

  } catch (error) {
    console.error("GitHub connection test error:", error)

    return NextResponse.json(
      { 
        success: false,
        error: "GitHub connection failed",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}
