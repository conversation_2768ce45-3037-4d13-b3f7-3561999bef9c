import { Mail, Github, Globe, Share2 } from "lucide-react"
import ContactForm from "@/components/ui/contact-form"
import { type PersonalInfo, type SocialLink } from "@/actions/portfolio-actions"

interface ContactSectionProps {
  personalInfo: PersonalInfo
  socialLinks: SocialLink[]
}

export default function ContactSection({ personalInfo, socialLinks }: ContactSectionProps) {
  return (
    <section id="contact" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
      <h2 className="text-3xl font-bold mb-12 flex items-center">
        <Mail className="mr-2 h-6 w-6" /> Get in Touch
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        <div>
          <p className="text-gray-600 mb-6">
            Interested in working together? Feel free to reach out for collaborations or just a friendly hello.
          </p>
          <div className="flex flex-col gap-4">
            <a
              href={`mailto:${personalInfo.email}`}
              className="flex items-center gap-2 text-gray-600 hover:text-rose-500 transition-colors"
            >
              <Mail className="h-5 w-5" /> {personalInfo.email}
            </a>
            {socialLinks.map((link) => (
              <a
                key={link.platform}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-gray-600 hover:text-rose-500 transition-colors"
              >
                {link.platform === "GitHub" && <Github className="h-5 w-5" />}
                {link.platform === "Website" && <Globe className="h-5 w-5" />}
                {!["GitHub", "Website"].includes(link.platform) && <Share2 className="h-5 w-5" />}
                {link.platform === "Website" ? personalInfo.website : link.url}
              </a>
            ))}
          </div>
        </div>
        <ContactForm />
      </div>
    </section>
  )
}
