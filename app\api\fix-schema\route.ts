import { createServerSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = await createServerSupabaseClient()

    // Kiểm tra xem cột profile_image đã tồn tại chưa
    const { data: columnInfo, error: columnCheckError } = await supabase.rpc("get_column_info", {
      table_name: "personal_info",
      column_name: "profile_image",
    })

    // Nếu không thể kiểm tra bằng RPC, thử cách khác
    if (columnCheckError) {
      console.log("Không thể kiểm tra cột bằng RPC, thử phương pháp thay thế...")

      // Thêm cột profile_image nếu chưa tồn tại
      try {
        const { error: alterError } = await supabase.query(`
          ALTER TABLE personal_info 
          ADD COLUMN IF NOT EXISTS profile_image TEXT;
        `)

        if (alterError) {
          console.error("Lỗi khi thêm cột profile_image:", alterError)
          return NextResponse.json(
            {
              success: false,
              message: "Không thể thêm cột profile_image",
              error: alterError.message,
            },
            { status: 500 },
          )
        }

        return NextResponse.json({
          success: true,
          message: "Đã thêm cột profile_image vào bảng personal_info",
        })
      } catch (error) {
        console.error("Lỗi khi thực hiện truy vấn SQL:", error)
        return NextResponse.json(
          {
            success: false,
            message: "Lỗi khi thực hiện truy vấn SQL",
            error: error instanceof Error ? error.message : "Lỗi không xác định",
          },
          { status: 500 },
        )
      }
    }

    // Nếu cột không tồn tại, thêm vào
    if (!columnInfo || columnInfo.length === 0) {
      const { error: alterError } = await supabase.query(`
        ALTER TABLE personal_info 
        ADD COLUMN profile_image TEXT;
      `)

      if (alterError) {
        console.error("Lỗi khi thêm cột profile_image:", alterError)
        return NextResponse.json(
          {
            success: false,
            message: "Không thể thêm cột profile_image",
            error: alterError.message,
          },
          { status: 500 },
        )
      }

      return NextResponse.json({
        success: true,
        message: "Đã thêm cột profile_image vào bảng personal_info",
      })
    }

    return NextResponse.json({
      success: true,
      message: "Cột profile_image đã tồn tại",
      columnInfo,
    })
  } catch (error) {
    console.error("Lỗi khi kiểm tra/cập nhật schema:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Lỗi khi kiểm tra/cập nhật schema",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
