import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function GET() {
  try {
    // Tạo Supabase client với thông tin mới
    const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

    // Kiểm tra kết nối
    const { data: authData, error: authError } = await supabase.auth.getSession()

    if (authError) {
      return NextResponse.json(
        {
          success: false,
          error: authError.message,
          message: "Không thể kết nối đến Supabase với thông tin mới",
        },
        { status: 500 },
      )
    }

    return NextResponse.json({
      success: true,
      message: "Kết nối thành công đến Supabase với thông tin mới",
      config: {
        url: config.supabase.url,
        anonKey: `${config.supabase.anonKey.substring(0, 10)}...`,
        serviceRoleKey: `${config.supabase.serviceRoleKey.substring(0, 10)}...`,
      },
    })
  } catch (error) {
    console.error("Lỗi khi kiểm tra kết nối Supabase:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}

