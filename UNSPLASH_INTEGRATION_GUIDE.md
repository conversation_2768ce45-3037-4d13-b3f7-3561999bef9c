# Hướng dẫn Unsplash Integration cho Project Images

## Tổng quan

Portfolio admin giờ đây sử dụng Unsplash API để cung cấp hình ảnh chất lượng cao và miễn phí cho projects thay vì upload file trực tiếp.

## Tính năng mới

### ✅ **Đã implement:**

1. **UnsplashImagePicker Component**
   - Search images từ Unsplash API
   - Grid layout với preview
   - Load more functionality
   - Image selection với visual feedback

2. **SmartUnsplashPicker Component**
   - AI-powered search suggestions
   - Analyze project title, description, tags
   - Auto-detect project type và technology
   - Smart keyword extraction

3. **Fallback System**
   - Sử dụng Picsum Photos khi Unsplash API không available
   - Graceful degradation
   - No API key required cho development

4. **Test Interface**
   - `/admin/test-unsplash` - Test both components
   - Mock project data configuration
   - Real-time suggestion testing

## Setup Instructions

### **1. Unsplash API Key (Optional)**

#### **Get API Key:**
1. T<PERSON><PERSON> cập: https://unsplash.com/developers
2. Tạo tài khoản developer
3. Create new application
4. Copy Access Key

#### **Configure Environment:**
```bash
# Add to .env.local
UNSPLASH_ACCESS_KEY=your_unsplash_access_key
```

#### **Without API Key:**
- App sẽ tự động sử dụng Picsum Photos
- Vẫn có đầy đủ functionality
- Perfect cho development và testing

### **2. Component Usage**

#### **Basic Unsplash Picker:**
```tsx
import UnsplashImagePicker from "@/components/unsplash-image-picker"

<UnsplashImagePicker
  onImageSelected={(url, imageData) => {
    setImageUrl(url)
    console.log("Image data:", imageData)
  }}
  currentImageUrl={currentImage}
  searchPlaceholder="Search for images..."
  defaultQuery="technology"
/>
```

#### **Smart Unsplash Picker:**
```tsx
import SmartUnsplashPicker from "@/components/smart-unsplash-picker"

<SmartUnsplashPicker
  onImageSelected={(url, imageData) => {
    setImageUrl(url)
  }}
  currentImageUrl={currentImage}
  projectTitle="React Portfolio"
  projectDescription="Modern portfolio website"
  projectTags={["react", "typescript", "web"]}
/>
```

## Smart Suggestions Algorithm

### **Project Type Detection:**
- **Web:** "web", "website", "frontend", "backend"
- **Mobile:** "mobile", "app", "ios", "android"
- **API:** "api", "server", "database"
- **AI:** "ai", "machine learning", "ml"
- **Game:** "game", "gaming"
- **Blockchain:** "blockchain", "crypto"
- **E-commerce:** "ecommerce", "shop", "store"

### **Technology Detection:**
- **React:** "react" → suggests "react", "javascript", "frontend"
- **Vue:** "vue" → suggests "vue.js", "spa", "javascript"
- **Python:** "python" → suggests "python", "data science", "ai"
- **Node:** "node" → suggests "node.js", "backend", "server"

### **Keyword Extraction:**
1. Extract từ project tags
2. Extract từ project title (words > 3 chars)
3. Extract từ description (words > 4 chars, limit 5)
4. Combine và deduplicate
5. Limit to 8 keywords

## API Endpoints

### **GET /api/unsplash/search**
```typescript
// Search images
GET /api/unsplash/search?query=technology&page=1&per_page=12

Response:
{
  results: UnsplashImage[],
  total: number,
  total_pages: number,
  warning?: string // If using fallback
}
```

### **POST /api/unsplash/search**
```typescript
// Get smart suggestions
POST /api/unsplash/search
Body: {
  projectType: "web" | "mobile" | "api" | ...,
  technology: "react" | "vue" | "python" | ...,
  keywords: string[]
}

Response:
{
  suggestions: string[],
  recommended: string
}
```

## Image Data Structure

### **UnsplashImage Interface:**
```typescript
interface UnsplashImage {
  id: string
  urls: {
    small: string      // 400x300
    regular: string    // 800x600 (recommended)
    full: string       // 1200x900
    thumb: string      // 200x150
  }
  alt_description: string | null
  description: string | null
  user: {
    name: string
    username: string
  }
  links: {
    html: string      // Unsplash page URL
  }
  width: number
  height: number
}
```

## Usage in Projects

### **Projects Page Integration:**
- ✅ Replace ImageUpload với SmartUnsplashPicker
- ✅ Auto-analyze project data cho suggestions
- ✅ Real-time suggestions khi user type
- ✅ Visual feedback cho selected image

### **Workflow:**
1. **User enters project info** (title, description, tags)
2. **Smart suggestions generated** based on project data
3. **User clicks suggestion** hoặc search manually
4. **Images loaded** từ Unsplash/Picsum
5. **User selects image** → URL saved to project
6. **Image displays** on homepage và project cards

## Benefits

### **1. High Quality Images:**
- ✅ Professional photography từ Unsplash
- ✅ Consistent quality và resolution
- ✅ No copyright issues
- ✅ Free to use

### **2. Better User Experience:**
- ✅ No file upload required
- ✅ Instant preview
- ✅ Smart suggestions
- ✅ Fast loading

### **3. Storage Efficiency:**
- ✅ No storage space used
- ✅ No bandwidth for uploads
- ✅ CDN delivery từ Unsplash
- ✅ Automatic optimization

### **4. Developer Friendly:**
- ✅ Easy integration
- ✅ Fallback system
- ✅ No API key required cho development
- ✅ Comprehensive testing tools

## Testing

### **Test Interface:**
1. **Truy cập:** `/admin/test-unsplash`
2. **Configure project data:** title, description, tags
3. **Test smart suggestions:** See how algorithm works
4. **Compare components:** Basic vs Smart picker
5. **Verify image selection:** Check URL và metadata

### **Test Scenarios:**
- ✅ **With Unsplash API key:** Real Unsplash images
- ✅ **Without API key:** Picsum fallback images
- ✅ **Different project types:** Web, mobile, AI, etc.
- ✅ **Various technologies:** React, Python, etc.
- ✅ **Edge cases:** Empty data, network errors

## Migration from ImageUpload

### **Before (ImageUpload):**
```tsx
<ImageUpload
  onImageUploaded={handleImageUploaded}
  currentImageUrl={formData.imageUrl}
  bucketName="projects"
  folderPath="images"
/>
```

### **After (SmartUnsplashPicker):**
```tsx
<SmartUnsplashPicker
  onImageSelected={handleImageSelected}
  currentImageUrl={formData.imageUrl}
  projectTitle={formData.title}
  projectDescription={formData.description}
  projectTags={formData.tags}
/>
```

### **Changes Required:**
1. ✅ Replace import statement
2. ✅ Change `onImageUploaded` → `onImageSelected`
3. ✅ Add project data props
4. ✅ Remove bucket/folder props

## Kết luận

Unsplash integration mang lại:
- ✅ **Better images:** Professional quality
- ✅ **Better UX:** No uploads, instant preview
- ✅ **Smart features:** AI-powered suggestions
- ✅ **Zero storage:** No file management
- ✅ **Free to use:** No costs
- ✅ **Easy setup:** Optional API key

**Projects giờ đây có hình ảnh đẹp và professional mà không cần upload files!** 🎨✨
