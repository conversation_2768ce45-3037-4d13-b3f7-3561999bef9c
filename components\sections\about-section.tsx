import { User } from "lucide-react"
import TechStack from "@/components/tech-stack"
import { type PersonalInfo } from "@/actions/portfolio-actions"

interface AboutSectionProps {
  personalInfo: PersonalInfo
  technologies: string[]
}

export default function AboutSection({ personalInfo, technologies }: AboutSectionProps) {
  return (
    <section id="about" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        <div>
          <h2 className="text-3xl font-bold mb-6 flex items-center">
            <User className="mr-2 h-6 w-6" /> About Me
          </h2>
          <p className="text-gray-600 mb-4">{personalInfo.bio}</p>
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-4">Tech Stack</h3>
            <TechStack technologies={technologies} />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="aspect-square bg-gray-100 rounded-lg"></div>
          <div className="aspect-square bg-gray-100 rounded-lg mt-8"></div>
          <div className="aspect-square bg-gray-100 rounded-lg mt-4"></div>
          <div className="aspect-square bg-gray-100 rounded-lg"></div>
        </div>
      </div>
    </section>
  )
}
