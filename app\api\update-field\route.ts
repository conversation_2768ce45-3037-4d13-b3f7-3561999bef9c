import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { revalidatePath } from "next/cache"

export async function POST(request: Request) {
  try {
    const supabase = createServiceSupabaseClient()
    const { field, value } = await request.json()

    if (!field || value === undefined) {
      return NextResponse.json(
        {
          success: false,
          message: "Thiếu trường hoặc giá trị",
        },
        { status: 400 },
      )
    }

    console.log(`Cập nhật trường ${field} với giá trị:`, value)

    // Lấy ID của bản ghi hiện tại
    const { data: existingData, error: fetchError } = await supabase.from("personal_info").select("id").limit(1)

    if (fetchError || !existingData || existingData.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: fetchError?.message || "<PERSON>hông tìm thấy dữ liệu",
          message: "<PERSON><PERSON><PERSON>ng thể lấy ID bản ghi",
        },
        { status: 500 },
      )
    }

    const recordId = existingData[0].id

    // Tạo đối tượng cập nhật chỉ với một trường
    const updateData: Record<string, any> = {
      updated_at: new Date().toISOString(),
    }

    // Xử lý trường hợp đặc biệt cho profile_image
    if (field === "profileImage") {
      updateData["profile_image"] = value
    } else {
      updateData[field] = value
    }

    console.log("Dữ liệu cập nhật:", updateData)

    // Thực hiện cập nhật
    const { error: updateError } = await supabase.from("personal_info").update(updateData).eq("id", recordId)

    if (updateError) {
      console.error("Lỗi cập nhật:", updateError)
      return NextResponse.json(
        {
          success: false,
          error: updateError.message,
          message: `Không thể cập nhật trường ${field}`,
        },
        { status: 500 },
      )
    }

    // Revalidate các đường dẫn
    revalidatePath("/")
    revalidatePath("/admin/personal-info")
    revalidatePath("/admin/dashboard")

    return NextResponse.json({
      success: true,
      message: `Đã cập nhật trường ${field} thành công`,
    })
  } catch (error) {
    console.error("Lỗi khi cập nhật trường:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Đã xảy ra lỗi không mong muốn",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}
