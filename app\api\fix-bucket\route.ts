import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Thông tin kết nối Supabase mới
const SUPABASE_URL = "https://oklfvgcouhwrjtwyttjm.supabase.co"
const SUPABASE_SERVICE_ROLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9rbGZ2Z2NvdWh3cmp0d3l0dGptIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTUxMDIzNSwiZXhwIjoyMDU1MDg2MjM1fQ.E8F4zj-cS-zjyhjuWnFEgPcyFJxFEZLBd3tzps99gHY"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const bucketName = body.bucketName || "general"

    console.log(`Đang sửa lỗi bucket '${bucketName}' với quyền admin...`)

    // Tạo Supabase client với service role key để có quyền admin
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Kiểm tra xem bucket đã tồn tại chưa
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()

    if (listError) {
      console.error("Lỗi khi liệt kê buckets:", listError)
      return NextResponse.json(
        {
          success: false,
          error: `Không thể liệt kê buckets: ${listError.message}`,
        },
        { status: 500 }
      )
    }

    const bucketExists = buckets?.some((bucket) => bucket.name === bucketName)

    if (bucketExists) {
      console.log(`Bucket '${bucketName}' đã tồn tại, đang cập nhật quyền...`)

      // Cập nhật quyền cho bucket
      const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
        public: true,
        fileSizeLimit: 10485760, // 10MB
      })

      if (updateError) {
        console.error("Lỗi khi cập nhật quyền bucket:", updateError)
        return NextResponse.json(
          {
            success: false,
            error: `Không thể cập nhật quyền bucket: ${updateError.message}`,
          },
          { status: 500 }
        )
      }

      console.log(`Đã cập nhật quyền cho bucket '${bucketName}' thành công`)
    } else {
      console.log(`Bucket '${bucketName}' chưa tồn tại, đang tạo mới...`)

      // Tạo bucket mới
      const { error: createError } = await supabase.storage.createBucket(bucketName, {
        public: true,
        fileSizeLimit: 10485760, // 10MB
      })

      if (createError) {
        console.error("Lỗi khi tạo bucket:", createError)
        return NextResponse.json(
          {
            success: false,
            error: `Không thể tạo bucket: ${createError.message}`,
          },
          { status: 500 }
        )
      }

      console.log(`Đã tạo bucket '${bucketName}' thành công`)
    }

    // Thử upload một file test để xác nhận quyền ghi
    const testContent = `Test file created at ${new Date().toISOString()}`
    const testFileName = `admin-fix-test-${Date.now()}.txt`
    const filePath = `test/${testFileName}`

    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(filePath, new Blob([testContent]), { upsert: true })

    if (uploadError) {
      console.error("Lỗi khi upload file test:", uploadError)
      return NextResponse.json(
        {
          success: false,
          error: `Không thể upload file test: ${uploadError.message}`,
        },
        { status: 500 }
      )
    }

    console.log(`Đã upload file test thành công: ${filePath}`)

    // Lấy URL công khai
    const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)

    return NextResponse.json({
      success: true,
      message: `Đã sửa lỗi bucket '${bucketName}' thành công`,
      testFile: {
        filePath,
        publicUrl: urlData.publicUrl,
      },
    })
  } catch (error) {
    console.error("Lỗi trong API fix-bucket:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 }
    )
  }
}
