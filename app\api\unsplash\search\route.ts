import { NextResponse } from "next/server"

// Unsplash API configuration
const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY
const UNSPLASH_API_URL = "https://api.unsplash.com"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("query")
    const page = searchParams.get("page") || "1"
    const perPage = searchParams.get("per_page") || "12"

    if (!query) {
      return NextResponse.json(
        { error: "Query parameter is required" },
        { status: 400 }
      )
    }

    // Check if Unsplash API key is configured
    if (!UNSPLASH_ACCESS_KEY) {
      console.warn("Unsplash API key not configured, using fallback images")
      
      // Return mock/fallback images for development
      const fallbackImages = generateFallbackImages(query, parseInt(page), parseInt(perPage))
      return NextResponse.json(fallbackImages)
    }

    // Make request to Unsplash API
    const unsplashUrl = new URL(`${UNSPLASH_API_URL}/search/photos`)
    unsplashUrl.searchParams.set("query", query)
    unsplashUrl.searchParams.set("page", page)
    unsplashUrl.searchParams.set("per_page", perPage)
    unsplashUrl.searchParams.set("orientation", "landscape") // Prefer landscape for project images

    const response = await fetch(unsplashUrl.toString(), {
      headers: {
        "Authorization": `Client-ID ${UNSPLASH_ACCESS_KEY}`,
        "Accept-Version": "v1"
      }
    })

    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      results: data.results || [],
      total: data.total || 0,
      total_pages: data.total_pages || 0
    })

  } catch (error) {
    console.error("Unsplash search error:", error)
    
    // Return fallback images on error
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("query") || "technology"
    const page = searchParams.get("page") || "1"
    const perPage = searchParams.get("per_page") || "12"
    
    const fallbackImages = generateFallbackImages(query, parseInt(page), parseInt(perPage))
    
    return NextResponse.json({
      ...fallbackImages,
      warning: "Using fallback images - Unsplash API unavailable"
    })
  }
}

// Generate fallback images when Unsplash API is not available
function generateFallbackImages(query: string, page: number, perPage: number) {
  const categories = [
    "technology", "web", "mobile", "design", "coding", "computer", 
    "software", "app", "digital", "innovation", "startup", "business"
  ]
  
  const getImageUrl = (width: number, height: number, seed: string) => {
    return `https://picsum.photos/seed/${seed}/${width}/${height}`
  }

  const results = []
  const startIndex = (page - 1) * perPage

  for (let i = 0; i < perPage; i++) {
    const index = startIndex + i
    const seed = `${query}-${index}`
    const width = 800
    const height = 600
    
    results.push({
      id: `fallback-${seed}`,
      urls: {
        small: getImageUrl(400, 300, seed),
        regular: getImageUrl(width, height, seed),
        full: getImageUrl(1200, 900, seed),
        thumb: getImageUrl(200, 150, seed)
      },
      alt_description: `${query} related image`,
      description: `Fallback image for ${query}`,
      user: {
        name: "Picsum Photos",
        username: "picsum"
      },
      links: {
        html: "https://picsum.photos"
      },
      width,
      height
    })
  }

  return {
    results,
    total: 1000, // Mock total
    total_pages: Math.ceil(1000 / perPage)
  }
}

// Popular search suggestions for different project types
export async function POST(request: Request) {
  try {
    const { projectType, technology } = await request.json()
    
    const suggestions = {
      web: ["web development", "website design", "responsive design", "frontend", "backend"],
      mobile: ["mobile app", "smartphone", "ios", "android", "mobile ui"],
      desktop: ["desktop application", "computer software", "productivity", "workspace"],
      api: ["api development", "server", "database", "cloud computing", "microservices"],
      ai: ["artificial intelligence", "machine learning", "neural network", "data science"],
      blockchain: ["blockchain", "cryptocurrency", "bitcoin", "ethereum", "smart contracts"],
      game: ["game development", "gaming", "3d graphics", "unity", "game design"],
      ecommerce: ["ecommerce", "online shopping", "retail", "payment", "shopping cart"],
      default: ["technology", "innovation", "digital", "modern", "futuristic"]
    }

    const techSuggestions = {
      react: ["react", "javascript", "frontend", "component", "jsx"],
      vue: ["vue.js", "vuejs", "frontend", "spa", "javascript"],
      angular: ["angular", "typescript", "frontend", "spa", "google"],
      node: ["node.js", "nodejs", "backend", "server", "javascript"],
      python: ["python", "programming", "data science", "ai", "backend"],
      java: ["java", "enterprise", "spring", "backend", "programming"],
      csharp: ["c#", "dotnet", ".net", "microsoft", "enterprise"],
      php: ["php", "web development", "backend", "server", "programming"],
      default: ["coding", "programming", "development", "software"]
    }

    const projectSuggestions = suggestions[projectType as keyof typeof suggestions] || suggestions.default
    const technologySuggestions = techSuggestions[technology as keyof typeof techSuggestions] || techSuggestions.default
    
    const combinedSuggestions = [...new Set([...projectSuggestions, ...technologySuggestions])]
    
    return NextResponse.json({
      suggestions: combinedSuggestions,
      recommended: combinedSuggestions[0] // First suggestion as default
    })

  } catch (error) {
    console.error("Suggestions error:", error)
    return NextResponse.json(
      { error: "Failed to get suggestions" },
      { status: 500 }
    )
  }
}
