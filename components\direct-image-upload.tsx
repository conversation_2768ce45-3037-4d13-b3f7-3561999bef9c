"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, ImageIcon, Loader2, AlertCircle, RefreshCw } from "lucide-react"
import { createClientSupabaseClient } from "@/utils/supabase/client"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface DirectImageUploadProps {
  onImageUploaded: (url: string) => void
  currentImageUrl?: string
  bucketName?: string
  folderPath?: string
  className?: string
}

export default function DirectImageUpload({
  onImageUploaded,
  currentImageUrl,
  bucketName = "general", // Mặc định là bucket general
  folderPath = "images",
  className,
}: DirectImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null)
  const [error, setError] = useState<string | null>(null)
  const [isBucketReady, setIsBucketReady] = useState(false)
  const [isCheckingBucket, setIsCheckingBucket] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Kiểm tra bucket khi component được tải
  useEffect(() => {
    checkBucketStatus()
  }, [bucketName])

  // Kiểm tra trạng thái bucket
  const checkBucketStatus = async () => {
    setIsCheckingBucket(true)
    setError(null)

    try {
      // Gọi API để kiểm tra bucket
      const response = await fetch("/api/check-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Lỗi khi kiểm tra bucket:", errorData)
        setIsBucketReady(false)
        setError(`Không thể kiểm tra bucket: ${errorData.error || "Lỗi không xác định"}`)
        return
      }

      const data = await response.json()
      setIsBucketReady(data.exists)

      if (!data.exists) {
        console.warn(`Bucket '${bucketName}' không tồn tại, sẽ thử tạo khi upload`)
      }
    } catch (err) {
      console.error("Lỗi khi kiểm tra bucket:", err)
      setIsBucketReady(false)
      setError("Không thể kiểm tra trạng thái bucket. Vui lòng thử lại sau.")
    } finally {
      setIsCheckingBucket(false)
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith("image/")) {
      setError("Vui lòng chọn file hình ảnh")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError("Kích thước hình ảnh phải nhỏ hơn 5MB")
      return
    }

    setError(null)
    setIsUploading(true)

    try {
      // Tạo URL xem trước
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)

      // Thử upload qua API route thay vì trực tiếp
      const formData = new FormData()
      formData.append("file", file)
      formData.append("bucketName", bucketName)
      formData.append("folderPath", folderPath)

      const response = await fetch("/api/upload-file", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Upload thất bại")
      }

      const data = await response.json()

      // Call the callback with the new URL
      onImageUploaded(data.publicUrl)
      console.log("Upload thành công:", data.publicUrl)
    } catch (err) {
      console.error("Error uploading image:", err)
      setError(err instanceof Error ? err.message : "Failed to upload image. Please try again.")
      // Revert to the previous image if there was one
      setPreviewUrl(currentImageUrl || null)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onImageUploaded("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={className}>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label htmlFor="direct-image-upload">Upload Image ({bucketName})</Label>
          <div className="flex items-center gap-2 text-xs">
            <span className="text-gray-500">Bucket Status:</span>
            <div className="flex items-center">
              <div
                className={`w-2 h-2 rounded-full mr-1 ${
                  isCheckingBucket ? "bg-yellow-500" : isBucketReady ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <span className={isCheckingBucket ? "text-yellow-600" : isBucketReady ? "text-green-600" : "text-red-600"}>
                {isCheckingBucket ? "Checking..." : isBucketReady ? "Ready" : "Not Ready"}
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={checkBucketStatus}
              disabled={isCheckingBucket}
            >
              <RefreshCw className={`h-3 w-3 ${isCheckingBucket ? "animate-spin" : ""}`} />
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Input
            ref={fileInputRef}
            id="direct-image-upload"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            disabled={isUploading}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" /> Select Image
              </>
            )}
          </Button>
          {previewUrl && (
            <Button type="button" variant="outline" size="icon" onClick={handleRemoveImage}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {error && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>

      {/* Image Preview */}
      {previewUrl ? (
        <div className="mt-4 relative aspect-video w-full max-w-md overflow-hidden rounded-md border border-gray-200">
          <img src={previewUrl || "/placeholder.svg"} alt="Preview" className="h-full w-full object-cover" />
        </div>
      ) : (
        <div className="mt-4 flex aspect-video w-full max-w-md items-center justify-center rounded-md border border-dashed border-gray-300 bg-gray-50">
          <div className="flex flex-col items-center gap-1 text-gray-500">
            <ImageIcon className="h-8 w-8" />
            <span className="text-sm">No image selected</span>
          </div>
        </div>
      )}
    </div>
  )
}
