"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Database, CheckCircle, XCircle, RefreshCw, AlertTriangle } from "lucide-react"

export default function TableStatusPage() {
  const [tableStatus, setTableStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const checkTables = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/check-tables")
      const data = await response.json()
      setTableStatus(data)
    } catch (error) {
      console.error("Failed to check tables:", error)
      setTableStatus({
        success: false,
        error: "Failed to check table status"
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkTables()
  }, [])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Table Status</h1>
          <p className="text-gray-500 mt-2">
            Check which tables exist in your Supabase database
          </p>
        </div>
        <Button onClick={checkTables} disabled={isLoading}>
          {isLoading ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {tableStatus && !tableStatus.success && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {tableStatus.error || "Failed to check table status"}
          </AlertDescription>
        </Alert>
      )}

      {tableStatus?.success && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Tables</p>
                    <p className="text-2xl font-bold">{tableStatus.summary?.totalTables || 0}</p>
                  </div>
                  <Database className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Existing Tables</p>
                    <p className="text-2xl font-bold text-green-600">{tableStatus.summary?.existingTables || 0}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Missing Tables</p>
                    <p className="text-2xl font-bold text-red-600">
                      {tableStatus.summary?.missingTables?.length || 0}
                    </p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Table Details</CardTitle>
              <CardDescription>
                Status of each required table in your database
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(tableStatus.tables || {}).map(([tableName, status]: [string, any]) => (
                  <div key={tableName} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {status.exists ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <div>
                        <p className="font-medium">{tableName}</p>
                        {status.exists && (
                          <p className="text-sm text-gray-500">
                            {status.count || 0} rows
                          </p>
                        )}
                        {!status.exists && status.error && (
                          <p className="text-sm text-red-600">
                            {status.error}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant={status.exists ? "default" : "destructive"}>
                        {status.exists ? "Exists" : "Missing"}
                      </Badge>
                      {status.exists && status.hasData && (
                        <Badge variant="secondary">Has Data</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {tableStatus.summary?.missingTables?.length > 0 && (
            <Alert className="bg-yellow-50 border-yellow-200">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-yellow-800">
                <p className="font-medium">Missing tables detected:</p>
                <p className="mt-1">
                  {tableStatus.summary.missingTables.join(", ")}
                </p>
                <p className="mt-2">
                  You need to create these tables before you can seed data. 
                  <a href="/admin/manual-setup" className="underline ml-1">
                    View setup instructions
                  </a>
                </p>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-4">
            <Button asChild>
              <a href="/admin/database-migration">
                <Database className="mr-2 h-4 w-4" />
                Setup Database
              </a>
            </Button>
            <Button asChild variant="outline">
              <a href="/admin/manual-setup">
                Manual Setup Guide
              </a>
            </Button>
          </div>
        </>
      )}
    </div>
  )
}
