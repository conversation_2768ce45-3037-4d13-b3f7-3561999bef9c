"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON>ader2, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExternalLink, ShieldOff } from "lucide-react"

export default function DisableRLSPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [sqlScript, setSqlScript] = useState<string>("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  // Tắt RLS
  const disableRLS = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/disable-rls", {
        method: "POST",
      })
      const data = await response.json()

      setResults(data.results)
      setSqlScript(data.sql_script)

      if (data.success) {
        setSuccess("Đã hoàn thành quá trình tắt RLS!")
      } else {
        setError(`Không thể tắt RLS: ${data.error || "Lỗi không xác định"}`)
      }
    } catch (err) {
      console.error("Lỗi khi tắt RLS:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsLoading(false)
    }
  }

  // Sao chép SQL script
  const copySqlScript = () => {
    navigator.clipboard.writeText(sqlScript)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tắt Row Level Security</h1>
          <p className="text-gray-500 mt-2">Tắt RLS cho các bảng trong Supabase</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Tắt Row Level Security (RLS)</CardTitle>
          <CardDescription>Tắt RLS cho tất cả các bảng để cho phép truy cập dữ liệu không bị hạn chế</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mb-4">
            <h3 className="font-medium text-amber-800 mb-2">Lưu ý quan trọng</h3>
            <p className="text-sm text-amber-700">
              Tắt RLS sẽ cho phép truy cập không hạn chế vào dữ liệu của bạn. Điều này hữu ích trong quá trình phát
              triển, nhưng bạn nên bật lại RLS và cấu hình các chính sách bảo mật phù hợp trước khi triển khai ứng dụng
              vào môi trường sản xuất.
            </p>
          </div>

          <Button onClick={disableRLS} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang tắt RLS...
              </>
            ) : (
              <>
                <ShieldOff className="mr-2 h-4 w-4" /> Tắt RLS
              </>
            )}
          </Button>
        </CardContent>
        {results && (
          <CardFooter>
            <div className="w-full space-y-4">
              <h3 className="font-medium">Kết quả:</h3>
              <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60 w-full">
                {JSON.stringify(results, null, 2)}
              </pre>

              <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                  <ExternalLink className="mr-2 h-4 w-4" /> Thực hiện thủ công trong SQL Editor
                </h3>
                <p className="text-sm text-blue-700 mb-2">
                  Nếu quá trình tự động không thành công, bạn có thể sao chép và thực thi SQL script sau trong SQL
                  Editor của Supabase Dashboard:
                </p>
                <div className="relative">
                  <pre className="bg-blue-100 p-3 rounded-md text-xs overflow-auto max-h-40">{sqlScript}</pre>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute top-2 right-2"
                    onClick={copySqlScript}
                    disabled={copied}
                  >
                    {copied ? (
                      <>
                        <Check className="mr-2 h-4 w-4" /> Đã sao chép
                      </>
                    ) : (
                      <>
                        <Copy className="mr-2 h-4 w-4" /> Sao chép
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
