"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Modal, ExternalLink, Settings, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface EditModeToggleProps {
  onModeChange: (usePageMode: boolean) => void
  defaultMode?: boolean
}

export default function EditModeToggle({ onModeChange, defaultMode = false }: EditModeToggleProps) {
  const [usePageMode, setUsePageMode] = useState(defaultMode)

  const handleToggle = (checked: boolean) => {
    setUsePageMode(checked)
    onModeChange(checked)
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Settings className="h-5 w-5" />
          Edit Mode Settings
        </CardTitle>
        <CardDescription>
          Choose how you want to edit projects
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="edit-mode"
              checked={usePageMode}
              onCheckedChange={handleToggle}
            />
            <Label htmlFor="edit-mode" className="font-medium">
              Use full page editor
            </Label>
          </div>
          <Badge variant={usePageMode ? "default" : "secondary"}>
            {usePageMode ? "Page Mode" : "Modal Mode"}
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className={`p-3 rounded-lg border ${!usePageMode ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'}`}>
            <div className="flex items-center gap-2 mb-2">
              <Modal className="h-4 w-4" />
              <span className="font-medium">Modal Mode</span>
              {!usePageMode && <Badge variant="default" className="text-xs">Active</Badge>}
            </div>
            <ul className="space-y-1 text-gray-600">
              <li>• Quick edits</li>
              <li>• Stays on same page</li>
              <li>• Compact interface</li>
              <li>• Scrollable content</li>
            </ul>
          </div>

          <div className={`p-3 rounded-lg border ${usePageMode ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'}`}>
            <div className="flex items-center gap-2 mb-2">
              <ExternalLink className="h-4 w-4" />
              <span className="font-medium">Page Mode</span>
              {usePageMode && <Badge variant="default" className="text-xs">Active</Badge>}
            </div>
            <ul className="space-y-1 text-gray-600">
              <li>• Full editing experience</li>
              <li>• Dedicated page</li>
              <li>• More space for content</li>
              <li>• Better for complex edits</li>
            </ul>
          </div>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Current mode:</strong> {usePageMode ? "Full page editor" : "Modal editor"}
            {usePageMode 
              ? " - Click 'Full Edit' to open projects in a dedicated page"
              : " - Click 'Quick Edit' to edit projects in a modal dialog"
            }
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}
