"use client"

import { useEffect, useState, useCallback, useRef } from "react"
import { motion, useMotionValue, useSpring } from "framer-motion"

export default function CustomCursor() {
  const [isVisible, setIsVisible] = useState(false)
  const [isClicking, setIsClicking] = useState(false)
  const [isHovering, setIsHovering] = useState(false)
  const [cursorVariant, setCursorVariant] = useState("default")
  
  // Use motion values for better performance
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  
  // Spring animations for smooth following
  const springConfig = { damping: 25, stiffness: 200, mass: 0.5 }
  const cursorX = useSpring(mouseX, springConfig)
  const cursorY = useSpring(mouseY, springConfig)
  
  // Refs for performance optimization
  const lastUpdateRef = useRef<number>(0)

  const updatePosition = useCallback((e: MouseEvent) => {
    // Throttle updates to 60fps max
    const now = performance.now()
    if (now - lastUpdateRef.current < 16) return
    
    lastUpdateRef.current = now
    mouseX.set(e.clientX)
    mouseY.set(e.clientY)

    if (!isVisible) {
      setIsVisible(true)
    }
  }, [isVisible, mouseX, mouseY])

  const handleMouseDown = useCallback(() => setIsClicking(true), [])
  const handleMouseUp = useCallback(() => setIsClicking(false), [])
  const handleMouseLeave = useCallback(() => setIsVisible(false), [])

  // Optimized hover detection with caching
  const interactiveSelectors = useRef([
    'button', 'a', 'input', 'textarea', '[role="button"]', '.cursor-pointer'
  ].join(','))
  
  const textSelectors = useRef([
    'h1', 'h2', 'h3', '.cursor-text'
  ].join(','))

  const handleMouseEnter = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement
    
    // Use cached selectors for better performance
    if (target.matches?.(interactiveSelectors.current) || target.closest(interactiveSelectors.current)) {
      if (!isHovering || cursorVariant !== "pointer") {
        setIsHovering(true)
        setCursorVariant("pointer")
      }
    } else if (target.matches?.(textSelectors.current)) {
      if (!isHovering || cursorVariant !== "text") {
        setIsHovering(true)
        setCursorVariant("text")
      }
    } else {
      if (isHovering) {
        setIsHovering(false)
        setCursorVariant("default")
      }
    }
  }, [isHovering, cursorVariant])

  useEffect(() => {
    if (typeof window === 'undefined') return

    window.addEventListener("mousemove", updatePosition, { passive: true })
    window.addEventListener("mousedown", handleMouseDown)
    window.addEventListener("mouseup", handleMouseUp)
    window.addEventListener("mouseleave", handleMouseLeave)
    document.addEventListener("mouseover", handleMouseEnter, { passive: true })

    return () => {
      window.removeEventListener("mousemove", updatePosition)
      window.removeEventListener("mousedown", handleMouseDown)
      window.removeEventListener("mouseup", handleMouseUp)
      window.removeEventListener("mouseleave", handleMouseLeave)
      document.removeEventListener("mouseover", handleMouseEnter)
    }
  }, [updatePosition, handleMouseDown, handleMouseUp, handleMouseLeave, handleMouseEnter])

  // Only show custom cursor on desktop
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    return () => {
      window.removeEventListener("resize", checkMobile)
    }
  }, [])

  if (isMobile || typeof window === 'undefined') return null

  return (
    <>
      {/* Main cursor ring */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9999] will-change-transform"
        style={{
          x: cursorX,
          y: cursorY,
          translateX: -20,
          translateY: -20,
          opacity: isVisible ? 1 : 0,
          scale: isClicking ? 0.8 : isHovering ? 1.5 : 1,
        }}
        transition={{
          scale: { type: "spring", damping: 20, stiffness: 300 },
          opacity: { duration: 0.2 }
        }}
      >
        <div className={`
          w-10 h-10 rounded-full border-2 transition-colors duration-200
          ${isHovering ? 'border-purple-500 bg-purple-500/10' : 'border-blue-500'}
          ${isClicking ? 'border-pink-500 bg-pink-500/20' : ''}
          ${cursorVariant === "text" ? 'border-green-500 bg-green-500/10' : ''}
        `} />
      </motion.div>

      {/* Inner cursor dot */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9998] mix-blend-difference will-change-transform"
        style={{
          x: cursorX,
          y: cursorY,
          translateX: -3,
          translateY: -3,
          opacity: isVisible ? 1 : 0,
          scale: isClicking ? 0.5 : 1,
        }}
        transition={{
          scale: { type: "spring", damping: 30, stiffness: 400 },
          opacity: { duration: 0.2 }
        }}
      >
        <div className="w-1.5 h-1.5 rounded-full bg-white" />
      </motion.div>

      {/* Optimized trailing particles - only render when visible */}
      {isVisible && (
        <>
          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9997] will-change-transform"
            style={{
              x: cursorX,
              y: cursorY,
              translateX: -2,
              translateY: -2,
              opacity: 0.6,
            }}
            transition={{ type: "spring", damping: 20, stiffness: 100 }}
          >
            <div className="w-1 h-1 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
          </motion.div>

          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9996] will-change-transform"
            style={{
              x: cursorX,
              y: cursorY,
              translateX: -1,
              translateY: -1,
              opacity: 0.4,
            }}
            transition={{ type: "spring", damping: 15, stiffness: 80 }}
          >
            <div className="w-0.5 h-0.5 rounded-full bg-gradient-to-r from-purple-400 to-pink-400" />
          </motion.div>
        </>
      )}

      {/* Optimized hover effect particles - reduced complexity */}
      {isHovering && isVisible && (
        <motion.div
          className="fixed top-0 left-0 pointer-events-none z-[9995] will-change-transform"
          style={{
            x: cursorX,
            y: cursorY,
            translateX: -15,
            translateY: -15,
          }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400/30 to-purple-400/30" />
        </motion.div>
      )}

      {/* Optimized click ripple effect */}
      {isClicking && (
        <motion.div
          className="fixed top-0 left-0 pointer-events-none z-[9993] will-change-transform"
          style={{
            x: cursorX,
            y: cursorY,
            translateX: -30,
            translateY: -30,
          }}
          animate={{
            scale: [0, 2],
            opacity: [0.8, 0],
          }}
          transition={{
            duration: 0.6,
            ease: "easeOut",
          }}
        >
          <div className="w-16 h-16 rounded-full border-2 border-pink-400" />
        </motion.div>
      )}
    </>
  )
}
