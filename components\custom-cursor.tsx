"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

export default function CustomCursor() {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isVisible, setIsVisible] = useState(false)
  const [isClicking, setIsClicking] = useState(false)
  const [isHovering, setIsHovering] = useState(false)
  const [cursorVariant, setCursorVariant] = useState("default")

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY })

      if (!isVisible) {
        setIsVisible(true)
      }
    }

    const handleMouseDown = () => setIsClicking(true)
    const handleMouseUp = () => setIsClicking(false)
    const handleMouseLeave = () => setIsVisible(false)

    const handleMouseEnter = (e: MouseEvent) => {
      const target = e.target as HTMLElement

      // Check for interactive elements
      if (
        target.tagName === "BUTTON" ||
        target.tagName === "A" ||
        target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.role === "button" ||
        target.classList.contains("cursor-pointer") ||
        target.closest("button") ||
        target.closest("a") ||
        target.closest("[role='button']")
      ) {
        setIsHovering(true)
        setCursorVariant("pointer")
      } else if (
        target.tagName === "H1" ||
        target.tagName === "H2" ||
        target.tagName === "H3" ||
        target.classList.contains("cursor-text")
      ) {
        setIsHovering(true)
        setCursorVariant("text")
      } else {
        setIsHovering(false)
        setCursorVariant("default")
      }
    }

    window.addEventListener("mousemove", updatePosition)
    window.addEventListener("mousedown", handleMouseDown)
    window.addEventListener("mouseup", handleMouseUp)
    window.addEventListener("mouseleave", handleMouseLeave)
    document.addEventListener("mouseover", handleMouseEnter)

    return () => {
      window.removeEventListener("mousemove", updatePosition)
      window.removeEventListener("mousedown", handleMouseDown)
      window.removeEventListener("mouseup", handleMouseUp)
      window.removeEventListener("mouseleave", handleMouseLeave)
      document.removeEventListener("mouseover", handleMouseEnter)
    }
  }, [isVisible])

  // Only show custom cursor on desktop
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    return () => {
      window.removeEventListener("resize", checkMobile)
    }
  }, [])

  if (isMobile) return null

  return (
    <>
      {/* Main cursor ring */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9999]"
        animate={{
          x: position.x - 20,
          y: position.y - 20,
          scale: isClicking ? 0.8 : isHovering ? 1.5 : 1,
        }}
        transition={{
          type: "spring",
          damping: 25,
          stiffness: 200,
          mass: 0.5,
        }}
        style={{ opacity: isVisible ? 1 : 0 }}
      >
        <div className={`
          w-10 h-10 rounded-full border-2 transition-all duration-300
          ${isHovering ? 'border-purple-500 bg-purple-500/10' : 'border-blue-500'}
          ${isClicking ? 'border-pink-500 bg-pink-500/20' : ''}
          ${cursorVariant === "text" ? 'border-green-500 bg-green-500/10' : ''}
        `} />
      </motion.div>

      {/* Inner cursor dot */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9998] mix-blend-difference"
        animate={{
          x: position.x - 3,
          y: position.y - 3,
          scale: isClicking ? 0.5 : 1,
        }}
        transition={{
          type: "spring",
          damping: 50,
          stiffness: 400,
        }}
        style={{ opacity: isVisible ? 1 : 0 }}
      >
        <div className="w-1.5 h-1.5 rounded-full bg-white" />
      </motion.div>

      {/* Trailing particles */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9997]"
        animate={{
          x: position.x - 2,
          y: position.y - 2,
        }}
        transition={{
          type: "spring",
          damping: 20,
          stiffness: 100,
          delay: 0.05,
        }}
        style={{ opacity: isVisible ? 0.6 : 0 }}
      >
        <div className="w-1 h-1 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
      </motion.div>

      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-[9996]"
        animate={{
          x: position.x - 1,
          y: position.y - 1,
        }}
        transition={{
          type: "spring",
          damping: 15,
          stiffness: 80,
          delay: 0.1,
        }}
        style={{ opacity: isVisible ? 0.4 : 0 }}
      >
        <div className="w-0.5 h-0.5 rounded-full bg-gradient-to-r from-purple-400 to-pink-400" />
      </motion.div>

      {/* Hover effect particles */}
      {isHovering && (
        <>
          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9995]"
            animate={{
              x: position.x - 15,
              y: position.y - 15,
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400/30 to-purple-400/30" />
          </motion.div>

          <motion.div
            className="fixed top-0 left-0 pointer-events-none z-[9994]"
            animate={{
              x: position.x - 25,
              y: position.y - 25,
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.2,
            }}
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-400/20 to-pink-400/20" />
          </motion.div>
        </>
      )}

      {/* Click ripple effect */}
      {isClicking && (
        <motion.div
          className="fixed top-0 left-0 pointer-events-none z-[9993]"
          animate={{
            x: position.x - 30,
            y: position.y - 30,
            scale: [0, 2],
            opacity: [0.8, 0],
          }}
          transition={{
            duration: 0.6,
            ease: "easeOut",
          }}
        >
          <div className="w-16 h-16 rounded-full border-2 border-pink-400" />
        </motion.div>
      )}
    </>
  )
}
