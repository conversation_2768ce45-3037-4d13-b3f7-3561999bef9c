import { createServiceSupabaseClient } from "./server"

export async function ensureStorageBucket(bucketName = "general") {
  try {
    const supabase = createServiceSupabaseClient()

    // Kiểm tra xem có thể truy cập Supabase client không
    if (!supabase) {
      console.error("Không thể khởi tạo Supabase client")
      return { success: false, error: "Không thể khởi tạo Supabase client" }
    }

    console.log(`Đang kiểm tra quyền truy cập Storage...`)

    // Kiểm tra quyền truy cập Storage API bằng cách liệt kê buckets trước
    try {
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()

      if (listError) {
        console.error("Lỗi khi liệt kê buckets:", listError)
        return {
          success: false,
          error: `Không có quyền truy cập Storage API: ${listError.message}`
        }
      }

      console.log(`<PERSON><PERSON> liệt kê được ${buckets?.length || 0} buckets`)

      // Kiểm tra xem bucket đã tồn tại chưa
      const bucketExists = buckets?.some(bucket => bucket.name === bucketName)

      if (bucketExists) {
        console.log(`Bucket '${bucketName}' đã tồn tại, đang cập nhật quyền...`)

        // Cập nhật quyền cho bucket
        try {
          const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
            public: true,
            fileSizeLimit: 10485760, // 10MB
          })

          if (updateError) {
            console.error("Lỗi khi cập nhật quyền bucket:", updateError)
            // Nếu không thể cập nhật, nhưng bucket tồn tại, vẫn coi là thành công
            return {
              success: true,
              warning: `Bucket tồn tại nhưng không thể cập nhật quyền: ${updateError.message}`
            }
          }

          console.log(`Bucket '${bucketName}' đã được cập nhật thành công`)
          return { success: true }
        } catch (updateErr) {
          console.error("Lỗi khi cập nhật bucket:", updateErr)
          // Nếu không thể cập nhật, nhưng bucket tồn tại, vẫn coi là thành công
          return {
            success: true,
            warning: "Bucket tồn tại nhưng không thể cập nhật quyền"
          }
        }
      } else {
        // Bucket chưa tồn tại, tạo mới
        console.log(`Bucket '${bucketName}' chưa tồn tại, đang tạo mới...`)

        try {
          const { error: createError } = await supabase.storage.createBucket(bucketName, {
            public: true,
            fileSizeLimit: 10485760, // 10MB
          })

          if (createError) {
            // Nếu lỗi là do bucket đã tồn tại (có thể do race condition), đây là kết quả tốt
            if (createError.message && createError.message.includes("already exists")) {
              console.log(`Bucket '${bucketName}' đã tồn tại (race condition), đang cập nhật quyền...`)

              // Cập nhật quyền cho bucket
              const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
                public: true,
                fileSizeLimit: 10485760, // 10MB
              })

              if (updateError) {
                console.error("Lỗi khi cập nhật quyền bucket:", updateError)
                return {
                  success: true,
                  warning: `Bucket tồn tại nhưng không thể cập nhật quyền: ${updateError.message}`
                }
              }

              return { success: true }
            } else {
              // Lỗi khác khi tạo bucket
              console.error("Lỗi khi tạo bucket:", createError)

              // Kiểm tra nếu lỗi là do RLS
              if (createError.message && createError.message.includes("row-level security")) {
                return {
                  success: false,
                  error: `Không thể tạo bucket do RLS: ${createError.message}`,
                  needsManualSetup: true,
                  manualInstructions: [
                    "Go to Supabase Dashboard > Storage",
                    "Create 'general' bucket manually",
                    "Set bucket to public",
                    "Configure storage policies for public access"
                  ]
                }
              }

              return {
                success: false,
                error: `Không thể tạo bucket: ${createError.message}`,
                needsManualSetup: true
              }
            }
          }

          console.log(`Bucket '${bucketName}' đã được tạo thành công`)
          return { success: true }
        } catch (createErr) {
          console.error("Lỗi khi tạo bucket:", createErr)
          return {
            success: false,
            error: `Lỗi khi tạo bucket: ${createErr instanceof Error ? createErr.message : "Lỗi không xác định"}`
          }
        }
      }
    } catch (error) {
      console.error("Lỗi khi tạo/kiểm tra bucket:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định khi tạo/kiểm tra bucket",
      }
    }
  } catch (error) {
    console.error("Lỗi khi đảm bảo storage bucket:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Lỗi không xác định khi đảm bảo storage bucket",
    }
  }
}
