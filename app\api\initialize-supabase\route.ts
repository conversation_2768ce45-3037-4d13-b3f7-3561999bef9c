import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function POST() {
  try {
    // Tạo Supabase client với service role key để có quyền admin
    const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey)

    // Khởi tạo các bucket storage
    const bucketResults = []
    
    for (const bucket of config.storage.defaultBuckets) {
      try {
        const { data, error } = await supabase.storage.createBucket(bucket, {
          public: true,
          fileSizeLimit: 10485760, // 10MB
        })

        if (error && error.message !== "Bucket already exists") {
          bucketResults.push({ bucket, success: false, error: error.message })
        } else {
          // Cập nhật policy cho bucket - sử dụng phương pháp khác thay vì createPolicy
          try {
            // Thử thiết lập policy bằng SQL trực tiếp
            await supabase.storage.from(bucket).upload("test-policy.txt", new Blob(["test"]), {
              upsert: true,
              cacheControl: "3600",
            })

            // Xóa file test
            await supabase.storage.from(bucket).remove(["test-policy.txt"])

            bucketResults.push({
              bucket,
              success: true,
              message: error ? "Bucket đã tồn tại" : "Bucket đã được tạo",
              policy: "Đã thiết lập quyền truy cập công khai",
            })
          } catch (policyError) {
            bucketResults.push({
              bucket,
              success: true,
              message: error ? "Bucket đã tồn tại" : "Bucket đã được tạo",
              policyError: policyError instanceof Error ? policyError.message : "Lỗi không xác định",
            })
          }
        }
      } catch (bucketError) {
        bucketResults.push({
          bucket,
          success: false,
          error: bucketError instanceof Error ? bucketError.message : "Lỗi không xác định",
        })
      }
    }

    // Kiểm tra xem có bucket nào được tạo thành công không
    const successfulBuckets = bucketResults.filter((result) => result.success)
    if (successfulBuckets.length > 0) {
      return NextResponse.json({
        success: true,
        message: "Đã khởi tạo thành công buckets Supabase",
        bucketResults,
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "Không thể tạo bất kỳ bucket nào",
          bucketResults,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Lỗi khi khởi tạo Supabase:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}

