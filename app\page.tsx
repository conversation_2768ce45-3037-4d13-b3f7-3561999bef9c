"use client"

import Link from "next/link"
import { ArrowRight, Code, Github, Globe, Mail, User, Share2, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ProjectCard from "@/components/project-card"
import CustomCursor from "@/components/custom-cursor"
import TechStack from "@/components/tech-stack"
import { usePortfolio } from "@/contexts/portfolio-context"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function Home() {
  const { data, isLoading, error } = usePortfolio()
  const { personalInfo, technologies, projects, socialLinks, githubStats } = data

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-rose-500 mb-4" />
        <p className="text-lg">Loading portfolio data...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <main className="min-h-screen bg-white">
      <CustomCursor />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 w-full p-6 flex justify-between items-center z-10 bg-white/80 backdrop-blur-sm">
        <Link href="/" className="text-xl font-medium">
          portfolio.
        </Link>
        <div className="flex items-center gap-6">
          <Link href="#about" className="text-sm hover:underline">
            About
          </Link>
          <Link href="#projects" className="text-sm hover:underline">
            Projects
          </Link>
          <Link href="#contact" className="text-sm hover:underline">
            Contact
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-16 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-5xl md:text-6xl font-bold mb-4">
              Hello, I'm <span className="text-rose-500">{personalInfo.name}</span>
            </h1>
            <p className="text-lg text-gray-600 mb-8">{personalInfo.role}</p>
            <div className="flex gap-4">
              <Button asChild>
                <Link href="#projects">
                  View Projects <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="#contact">Get in Touch</Link>
              </Button>
            </div>
          </div>
          <div className="relative">
            <div className="aspect-square bg-gray-100 rounded-full overflow-hidden">
              <img
                src={personalInfo.profileImage || "/placeholder.svg?height=500&width=500"}
                alt={`${personalInfo.name}'s profile`}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-rose-500 rounded-full"></div>
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-500 rounded-full"></div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-3xl font-bold mb-6 flex items-center">
              <User className="mr-2 h-6 w-6" /> About Me
            </h2>
            <p className="text-gray-600 mb-4">{personalInfo.bio}</p>
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Tech Stack</h3>
              <TechStack technologies={technologies} />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="aspect-square bg-gray-100 rounded-lg"></div>
            <div className="aspect-square bg-gray-100 rounded-lg mt-8"></div>
            <div className="aspect-square bg-gray-100 rounded-lg mt-4"></div>
            <div className="aspect-square bg-gray-100 rounded-lg"></div>
          </div>
        </div>
      </section>

      {/* GitHub Stats Section */}
      <section className="py-16 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto bg-gray-50">
        <h2 className="text-3xl font-bold mb-8 flex items-center">
          <Github className="mr-2 h-6 w-6" /> GitHub Stats
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Stars</h3>
            <p className="text-4xl font-bold text-rose-500">{githubStats.stars}</p>
            <p className="text-gray-500 mt-1">Total stars earned</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Repositories</h3>
            <p className="text-4xl font-bold text-rose-500">{githubStats.repos}</p>
            <p className="text-gray-500 mt-1">Public repositories</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold mb-2">Followers</h3>
            <p className="text-4xl font-bold text-rose-500">{githubStats.followers}</p>
            <p className="text-gray-500 mt-1">GitHub followers</p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold mb-12 flex items-center">
          <Code className="mr-2 h-6 w-6" /> Selected Projects
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              title={project.title}
              description={project.description}
              tags={project.tags}
              imageUrl={project.imageUrl}
            />
          ))}
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold mb-12 flex items-center">
          <Mail className="mr-2 h-6 w-6" /> Get in Touch
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <p className="text-gray-600 mb-6">
              Interested in working together? Feel free to reach out for collaborations or just a friendly hello.
            </p>
            <div className="flex flex-col gap-4">
              <a
                href={`mailto:${personalInfo.email}`}
                className="flex items-center gap-2 text-gray-600 hover:text-rose-500 transition-colors"
              >
                <Mail className="h-5 w-5" /> {personalInfo.email}
              </a>
              {socialLinks.map((link) => (
                <a
                  key={link.platform}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-gray-600 hover:text-rose-500 transition-colors"
                >
                  {link.platform === "GitHub" && <Github className="h-5 w-5" />}
                  {link.platform === "Website" && <Globe className="h-5 w-5" />}
                  {!["GitHub", "Website"].includes(link.platform) && <Share2 className="h-5 w-5" />}
                  {link.platform === "Website" ? personalInfo.website : link.url}
                </a>
              ))}
            </div>
          </div>
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-500"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-500"
                />
              </div>
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                Message
              </label>
              <textarea
                id="message"
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-500"
              ></textarea>
            </div>
            <Button type="submit" className="w-full md:w-auto">
              Send Message
            </Button>
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 md:px-12 lg:px-24 border-t">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 mb-4 md:mb-0">
            © {new Date().getFullYear()} {personalInfo.name} • {personalInfo.location} •{" "}
            <a href={`https://${personalInfo.website}`} className="hover:underline">
              {personalInfo.website}
            </a>
          </p>
          <div className="flex gap-6">
            {socialLinks.map((link) => (
              <a
                key={link.platform}
                href={link.url}
                className="text-gray-600 hover:text-rose-500 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <span className="sr-only">{link.platform}</span>
                {link.platform === "Facebook" && (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
                {link.platform === "GitHub" && (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
                {link.platform === "Medium" && (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z" />
                  </svg>
                )}
                {link.platform === "StackOverflow" && (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M18.986 21.865v-6.404h2.134V24H1.844v-8.539h2.13v6.404h15.012zM6.111 19.731H16.85v-2.137H6.111v2.137zm.259-4.852l10.48 2.189.451-2.07-10.478-2.187-.453 2.068zm1.359-5.056l9.705 4.53.903-1.95-9.706-4.53-.902 1.95zm2.715-4.785l8.217 6.855 1.359-1.62-8.216-6.853-1.36 1.618zM15.751 0l-1.746 1.294 6.405 8.604 1.746-1.294L15.749 0z" />
                  </svg>
                )}
              </a>
            ))}
          </div>
        </div>
      </footer>
    </main>
  )
}
