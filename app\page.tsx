"use client"

import { Loader2 } from "lucide-react"
import CustomCursor from "@/components/custom-cursor"
import { usePortfolio } from "@/contexts/portfolio-context"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Navigation from "@/components/layout/navigation"
import HeroSection from "@/components/sections/hero-section"
import AboutSection from "@/components/sections/about-section"
import GithubStatsSection from "@/components/sections/github-stats-section"
import ProjectsSection from "@/components/sections/projects-section"
import ContactSection from "@/components/sections/contact-section"
import Footer from "@/components/layout/footer"

export default function Home() {
  const { data, isLoading, error } = usePortfolio()
  const { personalInfo, technologies, projects, socialLinks, githubStats } = data

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-rose-500 mb-4" />
        <p className="text-lg">Loading portfolio data...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <main className="min-h-screen bg-white">
      <CustomCursor />
      <Navigation />

      <HeroSection personalInfo={personalInfo} />

      <AboutSection personalInfo={personalInfo} technologies={technologies} />

      <GithubStatsSection githubStats={githubStats} />

      <ProjectsSection projects={projects} />

      <ContactSection personalInfo={personalInfo} socialLinks={socialLinks} />

      <Footer personalInfo={personalInfo} socialLinks={socialLinks} />
    </main>
  )
}
