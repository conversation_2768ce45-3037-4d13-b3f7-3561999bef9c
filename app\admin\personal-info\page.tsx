"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { usePortfolio, type PersonalInfo } from "@/contexts/portfolio-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Loader2, Info } from "lucide-react"
import ImageUpload from "@/components/image-upload"
import DebugInfo from "@/components/debug-info"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function PersonalInfoPage() {
  const { data, updatePersonalInfo, isLoading, refreshData } = usePortfolio()
  const [formData, setFormData] = useState<PersonalInfo & { profileImage: string }>({
    ...data.personalInfo,
    profileImage: data.personalInfo.profileImage || "",
  })
  const [isSaving, setIsSaving] = useState(false)
  const [isDirectSaving, setIsDirectSaving] = useState(false)
  const [fieldUpdating, setFieldUpdating] = useState<string | null>(null)
  const [dbInfo, setDbInfo] = useState<any>(null)
  const [isLoadingDbInfo, setIsLoadingDbInfo] = useState(false)

  // Update form data when context data changes
  useEffect(() => {
    setFormData({
      ...data.personalInfo,
      profileImage: data.personalInfo.profileImage || "",
    })
  }, [data.personalInfo])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, profileImage: url }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      console.log("Submitting personal info:", formData)
      await updatePersonalInfo(formData)

      // Force refresh the data after update
      await refreshData?.()

      toast({
        title: "Personal information updated",
        description: "Your changes have been saved successfully.",
      })
    } catch (error) {
      console.error("Error updating personal info:", error)
      toast({
        title: "Error",
        description: "Failed to update personal information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDirectSubmit = async () => {
    setIsDirectSaving(true)

    try {
      const response = await fetch("/api/update-personal-info", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Failed to update personal information")
      }

      // Force refresh the data after update
      await refreshData?.()

      toast({
        title: "Personal information updated (Direct API)",
        description: "Your changes have been saved successfully.",
      })
    } catch (error) {
      console.error("Error in direct update:", error)
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to update personal information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDirectSaving(false)
    }
  }

  // Thêm hàm cập nhật từng trường riêng biệt
  const updateSingleField = async (field: string, value: any) => {
    setFieldUpdating(field)

    try {
      const response = await fetch("/api/update-field", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ field, value }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `Failed to update ${field}`)
      }

      // Force refresh the data after update
      await refreshData?.()

      toast({
        title: `Field "${field}" updated`,
        description: "Your change has been saved successfully.",
      })
    } catch (error) {
      console.error(`Error updating field ${field}:`, error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to update ${field}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setFieldUpdating(null)
    }
  }

  // Thêm hàm lấy thông tin database
  const fetchDatabaseInfo = async () => {
    setIsLoadingDbInfo(true)
    try {
      const response = await fetch("/api/debug-database")
      const data = await response.json()
      setDbInfo(data)
    } catch (error) {
      console.error("Error fetching database info:", error)
      toast({
        title: "Error",
        description: "Failed to fetch database information",
        variant: "destructive",
      })
    } finally {
      setIsLoadingDbInfo(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Personal Information</h1>
        <p className="text-gray-500 mt-2">Update your personal details</p>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          Nếu bạn gặp vấn đề khi cập nhật, hãy thử sử dụng nút "Cập nhật từng trường" bên cạnh mỗi trường.
        </AlertDescription>
      </Alert>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Edit Personal Information</CardTitle>
            <CardDescription>This information will be displayed on your portfolio</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <div className="flex gap-2">
                <Input id="name" name="name" value={formData.name} onChange={handleChange} required />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("name", formData.name)}
                  disabled={fieldUpdating === "name"}
                >
                  {fieldUpdating === "name" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Role/Title</Label>
              <div className="flex gap-2">
                <Input id="role" name="role" value={formData.role} onChange={handleChange} required />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("role", formData.role)}
                  disabled={fieldUpdating === "role"}
                >
                  {fieldUpdating === "role" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              <div className="flex gap-2">
                <Textarea id="bio" name="bio" value={formData.bio} onChange={handleChange} rows={4} required />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("bio", formData.bio)}
                  disabled={fieldUpdating === "bio"}
                >
                  {fieldUpdating === "bio" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="flex gap-2">
                <Input id="location" name="location" value={formData.location} onChange={handleChange} />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("location", formData.location)}
                  disabled={fieldUpdating === "location"}
                >
                  {fieldUpdating === "location" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <div className="flex gap-2">
                <Input id="website" name="website" value={formData.website} onChange={handleChange} />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("website", formData.website)}
                  disabled={fieldUpdating === "website"}
                >
                  {fieldUpdating === "website" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="flex gap-2">
                <Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} required />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("email", formData.email)}
                  disabled={fieldUpdating === "email"}
                >
                  {fieldUpdating === "email" ? <Loader2 className="h-4 w-4 animate-spin" /> : "Cập nhật"}
                </Button>
              </div>
            </div>

            {/* Profile Image Upload */}
            <div className="space-y-2">
              <Label>Profile Image</Label>
              <div className="flex flex-col gap-2">
                <ImageUpload
                  onImageUploaded={handleImageUploaded}
                  currentImageUrl={formData.profileImage}
                  bucketName="avatars"
                  folderPath="profiles"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => updateSingleField("profileImage", formData.profileImage)}
                  disabled={fieldUpdating === "profileImage"}
                  className="mt-2 self-start"
                >
                  {fieldUpdating === "profileImage" ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Cập nhật ảnh đại diện
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-wrap gap-2">
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                </>
              ) : (
                "Save All Changes"
              )}
            </Button>

            <Button type="button" variant="outline" onClick={handleDirectSubmit} disabled={isDirectSaving}>
              {isDirectSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving (Direct)...
                </>
              ) : (
                "Save via API"
              )}
            </Button>

            <Button
              type="button"
              variant="secondary"
              onClick={fetchDatabaseInfo}
              disabled={isLoadingDbInfo}
              className="ml-auto"
            >
              {isLoadingDbInfo ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...
                </>
              ) : (
                "Check Database"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {dbInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Database Information</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs overflow-auto max-h-96 bg-gray-50 p-2 rounded-md">
              {JSON.stringify(dbInfo, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <DebugInfo data={{ formData, personalInfo: data.personalInfo }} title="Personal Info Debug" />
      <Toaster />
    </div>
  )
}
