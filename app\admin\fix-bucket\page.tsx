"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Check, AlertTriangle } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function FixBucketPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [bucketName, setBucketName] = useState("general")
  const [result, setResult] = useState<any>(null)

  const fixBucket = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    setResult(null)

    try {
      const response = await fetch("/api/fix-bucket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ bucketName }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Không thể sửa lỗi bucket")
      }

      const data = await response.json()
      setSuccess(data.message || "Đã sửa lỗi bucket thành công")
      setResult(data)
    } catch (err) {
      console.error("Lỗi khi sửa lỗi bucket:", err)
      setError(err instanceof Error ? err.message : "Lỗi không xác định")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Sửa lỗi Bucket</h1>
        <p className="text-gray-500 mt-2">Sửa lỗi liên quan đến Supabase Storage Bucket</p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Sửa lỗi Bucket với quyền Admin</CardTitle>
          <CardDescription>
            Công cụ này sẽ sử dụng quyền admin để sửa lỗi bucket trong Supabase Storage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-3">
                <Label htmlFor="bucket-name">Tên Bucket</Label>
                <Input
                  id="bucket-name"
                  value={bucketName}
                  onChange={(e) => setBucketName(e.target.value)}
                  placeholder="Nhập tên bucket"
                />
              </div>
              <div className="flex items-end">
                <Button onClick={fixBucket} disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Đang sửa...
                    </>
                  ) : (
                    "Sửa Bucket"
                  )}
                </Button>
              </div>
            </div>

            {result && (
              <div className="mt-4 p-4 border rounded-md">
                <h3 className="font-medium mb-2">Kết quả:</h3>
                <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-auto max-h-60">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
