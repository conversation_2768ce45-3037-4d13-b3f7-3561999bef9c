import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { config } from "@/lib/config"

export async function GET() {
  try {
    // Tạo client với thông tin mới
    const supabaseUrl = `https://${config.supabase.projectId}.supabase.co`
    const supabase = createClient(supabaseUrl, config.supabase.anonKey)

    // Kiểm tra kết nối
    const { data, error } = await supabase.auth.getSession()

    if (error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          message: "Không thể kết nối đến Supabase với thông tin mới",
          config: {
            url: supabaseUrl,
            projectId: config.supabase.projectId,
            keyLength: config.supabase.anonKey.length,
          },
        },
        { status: 500 },
      )
    }

    return NextResponse.json({
      success: true,
      message: "<PERSON><PERSON><PERSON> nối thành công đến Supabase với thông tin mới",
      projectId: config.supabase.projectId,
      url: supabaseUrl,
    })
  } catch (error) {
    console.error("Lỗi khi kiểm tra kết nối Supabase:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      },
      { status: 500 },
    )
  }
}

