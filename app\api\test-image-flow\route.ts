import { createServiceSupabaseClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const supabase = createServiceSupabaseClient()
    const results = {
      databaseCheck: null as any,
      storageCheck: null as any,
      urlGeneration: null as any,
      recommendations: [] as string[]
    }

    // 1. Check database for personal_info and profile_image
    try {
      const { data: personalInfo, error: dbError } = await supabase
        .from('personal_info')
        .select('*')
        .limit(1)
        .single()

      results.databaseCheck = {
        success: !dbError && !!personalInfo,
        hasData: !!personalInfo,
        hasProfileImage: personalInfo && 'profile_image' in personalInfo,
        profileImageValue: personalInfo?.profile_image,
        profileImageType: typeof personalInfo?.profile_image,
        error: dbError?.message,
        fullData: personalInfo
      }

      if (!personalInfo) {
        results.recommendations.push("No personal info data found - run database migration")
      } else if (!personalInfo.profile_image) {
        results.recommendations.push("profile_image field is empty - upload an image")
      }
    } catch (error) {
      results.databaseCheck = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 2. Check storage buckets
    try {
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
      
      const generalBucket = buckets?.find(b => b.name === 'general')
      const avatarsBucket = buckets?.find(b => b.name === 'avatars')

      results.storageCheck = {
        success: !bucketsError,
        totalBuckets: buckets?.length || 0,
        hasGeneral: !!generalBucket,
        hasAvatars: !!avatarsBucket,
        buckets: buckets?.map(b => ({ name: b.name, public: b.public })) || [],
        error: bucketsError?.message
      }

      if (!generalBucket) {
        results.recommendations.push("'general' bucket missing - create it in storage setup")
      }
      if (!avatarsBucket) {
        results.recommendations.push("'avatars' bucket missing - create it for profile images")
      }
    } catch (error) {
      results.storageCheck = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 3. Test URL generation for existing profile image
    try {
      if (results.databaseCheck?.profileImageValue) {
        const profileImagePath = results.databaseCheck.profileImageValue
        
        // Check if it's already a full URL
        const isFullUrl = profileImagePath.startsWith('http')
        
        if (!isFullUrl) {
          // Try to generate public URL
          const { data: urlData } = supabase.storage
            .from('avatars')
            .getPublicUrl(profileImagePath)

          results.urlGeneration = {
            success: true,
            originalPath: profileImagePath,
            isFullUrl: false,
            generatedUrl: urlData.publicUrl,
            canGenerateUrl: true
          }
        } else {
          results.urlGeneration = {
            success: true,
            originalPath: profileImagePath,
            isFullUrl: true,
            generatedUrl: profileImagePath,
            canGenerateUrl: true
          }
        }
      } else {
        results.urlGeneration = {
          success: false,
          error: "No profile image path to test"
        }
      }
    } catch (error) {
      results.urlGeneration = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }

    // 4. Test actual image accessibility
    if (results.urlGeneration?.generatedUrl) {
      try {
        const imageUrl = results.urlGeneration.generatedUrl
        const response = await fetch(imageUrl, { method: 'HEAD' })
        
        results.urlGeneration.accessibility = {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          contentType: response.headers.get('content-type'),
          accessible: response.ok
        }

        if (!response.ok) {
          results.recommendations.push(`Image not accessible: ${response.status} ${response.statusText}`)
        }
      } catch (error) {
        results.urlGeneration.accessibility = {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error"
        }
        results.recommendations.push("Image URL not accessible - check storage policies")
      }
    }

    // Overall assessment
    const allChecksPass = results.databaseCheck?.success && 
                         results.storageCheck?.success && 
                         results.urlGeneration?.success &&
                         results.urlGeneration?.accessibility?.success

    return NextResponse.json({
      success: allChecksPass,
      message: allChecksPass 
        ? "All image flow checks passed - images should display correctly"
        : "Some issues found in image flow",
      results,
      recommendations: results.recommendations,
      summary: {
        databaseOk: results.databaseCheck?.success,
        storageOk: results.storageCheck?.success,
        urlGenerationOk: results.urlGeneration?.success,
        imageAccessible: results.urlGeneration?.accessibility?.success,
        hasProfileImage: !!results.databaseCheck?.profileImageValue
      }
    })

  } catch (error) {
    console.error("Image flow test error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Failed to test image flow"
      },
      { status: 500 }
    )
  }
}
