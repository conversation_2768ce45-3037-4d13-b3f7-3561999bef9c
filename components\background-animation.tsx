"use client"

import { useEffect, useState, useCallback, useRef, useMemo } from "react"
import { motion, useMotionValue, useSpring } from "framer-motion"

interface Particle {
  id: number
  x: number
  y: number
  size: number
  color: string
  speed: number
  direction: number
}

export default function BackgroundAnimation() {
  const [particles, setParticles] = useState<Particle[]>([])
  const [isVisible, setIsVisible] = useState(true)

  // Use motion values for mouse position
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  // Performance refs
  const animationFrameRef = useRef<number>()
  const lastUpdateRef = useRef<number>(0)
  const isReducedMotion = useRef(false)

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotion.current = mediaQuery.matches

    const handleChange = () => {
      isReducedMotion.current = mediaQuery.matches
      if (mediaQuery.matches) {
        setIsVisible(false)
      }
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Memoized colors array
  const colors = useMemo(() => [
    "rgba(59, 130, 246, 0.08)", // blue - reduced opacity
    "rgba(147, 51, 234, 0.08)", // purple
    "rgba(236, 72, 153, 0.08)", // pink
    "rgba(16, 185, 129, 0.08)", // emerald
    "rgba(245, 158, 11, 0.08)", // amber
  ], [])

  useEffect(() => {
    if (isReducedMotion.current || typeof window === 'undefined') return

    // Reduced particle count for better performance
    const initialParticles: Particle[] = []
    const particleCount = window.innerWidth < 768 ? 15 : 25 // Reduced from 50

    for (let i = 0; i < particleCount; i++) {
      initialParticles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        size: Math.random() * 3 + 1, // Reduced max size
        color: colors[Math.floor(Math.random() * colors.length)],
        speed: Math.random() * 0.3 + 0.1, // Reduced speed
        direction: Math.random() * Math.PI * 2,
      })
    }

    setParticles(initialParticles)

    // Throttled mouse move handler
    const handleMouseMove = useCallback((e: MouseEvent) => {
      const now = performance.now()
      if (now - lastUpdateRef.current < 32) return // Throttle to 30fps

      lastUpdateRef.current = now
      mouseX.set(e.clientX)
      mouseY.set(e.clientY)
    }, [mouseX, mouseY])

    window.addEventListener("mousemove", handleMouseMove, { passive: true })

    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [colors, mouseX, mouseY])

  // Optimized particle animation with RAF
  useEffect(() => {
    if (isReducedMotion.current || !isVisible || typeof window === 'undefined') return

    let lastTime = 0
    const animate = (currentTime: number) => {
      if (currentTime - lastTime >= 100) { // Update every 100ms instead of 50ms
        setParticles((prevParticles) =>
          prevParticles.map((particle) => {
            let newX = particle.x + Math.cos(particle.direction) * particle.speed
            let newY = particle.y + Math.sin(particle.direction) * particle.speed

            // Wrap around screen edges
            if (newX < 0) newX = window.innerWidth
            if (newX > window.innerWidth) newX = 0
            if (newY < 0) newY = window.innerHeight
            if (newY > window.innerHeight) newY = 0

            return {
              ...particle,
              x: newX,
              y: newY,
            }
          })
        )
        lastTime = currentTime
      }

      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isVisible])

  // Early return for reduced motion or invisible
  if (isReducedMotion.current || !isVisible) {
    return (
      <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/20 to-purple-50/20" />
      </div>
    )
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30" />

      {/* Optimized Animated Gradient Orbs - reduced opacity and size */}
      <motion.div
        className="absolute w-80 h-80 rounded-full opacity-15 will-change-transform"
        style={{
          background: "radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%)",
        }}
        animate={{
          x: [0, 80, 0],
          y: [0, -40, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        initial={{ x: "10%", y: "10%" }}
      />

      <motion.div
        className="absolute w-64 h-64 rounded-full opacity-12 will-change-transform"
        style={{
          background: "radial-gradient(circle, rgba(147, 51, 234, 0.2) 0%, transparent 70%)",
        }}
        animate={{
          x: [0, -60, 0],
          y: [0, 50, 0],
          scale: [1, 0.9, 1],
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 8,
        }}
        initial={{ x: "70%", y: "20%" }}
      />

      {/* Optimized Floating Particles - reduced animations */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full will-change-transform"
          style={{
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            left: particle.x,
            top: particle.y,
            transform: `scale(${0.8 + Math.sin(Date.now() * 0.001 + particle.id) * 0.2})`,
          }}
        />
      ))}

      {/* Optimized Mouse Interactive Glow */}
      <motion.div
        className="absolute w-24 h-24 rounded-full pointer-events-none will-change-transform"
        style={{
          background: "radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, transparent 70%)",
          x: mouseX,
          y: mouseY,
          translateX: -48,
          translateY: -48,
        }}
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.2, 0.4, 0.2],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Simplified Geometric Shapes - reduced count and complexity */}
      <motion.div
        className="absolute w-1.5 h-1.5 bg-blue-400/15 rounded-full will-change-transform"
        animate={{
          x: [0, 1200],
          y: [150, 250, 150],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
        initial={{ x: -10, y: 150 }}
      />

      <motion.div
        className="absolute w-1 h-1 bg-purple-400/20 rounded-full will-change-transform"
        animate={{
          x: [1200, 0],
          y: [350, 450, 350],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "linear",
          delay: 10,
        }}
        initial={{ x: 1210, y: 350 }}
      />
    </div>
  )
}
