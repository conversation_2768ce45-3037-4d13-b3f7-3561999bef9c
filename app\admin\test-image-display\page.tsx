"use client"

import { useState, useEffect } from "react"
import { usePortfolio } from "@/contexts/portfolio-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Check, X, AlertCircle, RefreshCw, Image as ImageIcon, ExternalLink } from "lucide-react"

export default function TestImageDisplayPage() {
  const { data, refreshData } = usePortfolio()
  const [imageFlowTest, setImageFlowTest] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    testImageFlow()
  }, [])

  const testImageFlow = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/test-image-flow")
      const result = await response.json()
      setImageFlowTest(result)
    } catch (error) {
      console.error("Failed to test image flow:", error)
      setImageFlowTest({
        success: false,
        error: "Failed to test image flow"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const StatusIcon = ({ success }: { success: boolean }) => (
    success ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-red-600" />
  )

  const StatusBadge = ({ success, label }: { success: boolean; label: string }) => (
    <Badge variant={success ? "default" : "destructive"} className="flex items-center gap-1">
      <StatusIcon success={success} />
      {label}
    </Badge>
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Test Image Display</h1>
          <p className="text-gray-500 mt-2">
            Test how images are displayed across the application
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={testImageFlow} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Test Image Flow
          </Button>
          <Button onClick={refreshData} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Portfolio Data Display */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Portfolio Data
            </CardTitle>
            <CardDescription>
              Current profile image from portfolio context
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Has Profile Image:</span>
              <StatusBadge 
                success={!!data.personalInfo?.profileImage} 
                label={data.personalInfo?.profileImage ? "Yes" : "No"} 
              />
            </div>
            
            {data.personalInfo?.profileImage && (
              <>
                <div className="space-y-2">
                  <span className="text-sm font-medium">Image URL:</span>
                  <div className="p-2 bg-gray-50 rounded text-xs font-mono break-all">
                    {data.personalInfo.profileImage}
                  </div>
                  <Button asChild variant="outline" size="sm">
                    <a href={data.personalInfo.profileImage} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-3 w-3" />
                      Open in new tab
                    </a>
                  </Button>
                </div>

                <div className="space-y-2">
                  <span className="text-sm font-medium">Image Preview:</span>
                  <div className="relative aspect-square w-32 overflow-hidden rounded-lg border border-gray-200 bg-gray-50">
                    <img 
                      src={data.personalInfo.profileImage} 
                      alt="Profile preview"
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        console.error("Image load error:", data.personalInfo.profileImage)
                        e.currentTarget.src = "/placeholder.svg?height=128&width=128"
                      }}
                    />
                  </div>
                </div>
              </>
            )}

            {!data.personalInfo?.profileImage && (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No profile image found</p>
                <p className="text-xs">Upload an image in Personal Info</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Image Flow Test Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Image Flow Test
            </CardTitle>
            <CardDescription>
              Technical test of image storage and accessibility
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {imageFlowTest ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Overall Status:</span>
                  <StatusBadge 
                    success={imageFlowTest.success} 
                    label={imageFlowTest.success ? "Pass" : "Fail"} 
                  />
                </div>

                {imageFlowTest.summary && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs">Database:</span>
                      <StatusBadge 
                        success={imageFlowTest.summary.databaseOk} 
                        label={imageFlowTest.summary.databaseOk ? "OK" : "Error"} 
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs">Storage:</span>
                      <StatusBadge 
                        success={imageFlowTest.summary.storageOk} 
                        label={imageFlowTest.summary.storageOk ? "OK" : "Error"} 
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs">URL Generation:</span>
                      <StatusBadge 
                        success={imageFlowTest.summary.urlGenerationOk} 
                        label={imageFlowTest.summary.urlGenerationOk ? "OK" : "Error"} 
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs">Accessibility:</span>
                      <StatusBadge 
                        success={imageFlowTest.summary.imageAccessible} 
                        label={imageFlowTest.summary.imageAccessible ? "OK" : "Error"} 
                      />
                    </div>
                  </div>
                )}

                {imageFlowTest.recommendations?.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        <p className="font-medium">Issues found:</p>
                        <ul className="list-disc list-inside text-xs space-y-1">
                          {imageFlowTest.recommendations.map((rec: string, index: number) => (
                            <li key={index}>{rec}</li>
                          ))}
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium">View Raw Test Data</summary>
                  <pre className="text-xs overflow-auto max-h-64 bg-gray-50 p-2 rounded-md mt-2">
                    {JSON.stringify(imageFlowTest, null, 2)}
                  </pre>
                </details>
              </>
            ) : (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Testing image flow...</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Hero Section Preview */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Hero Section Preview</CardTitle>
            <CardDescription>
              How the profile image appears on the homepage
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.personalInfo?.profileImage ? (
              <div className="flex items-center gap-6 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                <div className="relative">
                  <div className="aspect-square w-24 bg-gray-100 rounded-full overflow-hidden">
                    <img
                      src={data.personalInfo.profileImage}
                      alt={`${data.personalInfo.name}'s profile`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error("Hero image load error:", data.personalInfo.profileImage)
                        e.currentTarget.src = "/placeholder.svg?height=96&width=96"
                      }}
                    />
                  </div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-rose-500 rounded-full"></div>
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{data.personalInfo.name}</h2>
                  <p className="text-gray-600">{data.personalInfo.role}</p>
                  <p className="text-sm text-gray-500 mt-1">This is how it appears on the homepage</p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No profile image to preview</p>
                <p className="text-xs">Upload an image to see how it appears on the homepage</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
