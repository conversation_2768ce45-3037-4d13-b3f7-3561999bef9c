"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Check, X, AlertCircle, RefreshCw, Shield, Database, Upload, User } from "lucide-react"

export default function AuthDebugPage() {
  const { user, supabaseUser } = useAuth()
  const [authStatus, setAuthStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/auth-status")
      const data = await response.json()
      setAuthStatus(data)
    } catch (error) {
      console.error("Failed to check auth status:", error)
      setAuthStatus({
        success: false,
        error: "Failed to fetch auth status"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const StatusIcon = ({ success }: { success: boolean }) => (
    success ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-red-600" />
  )

  const StatusBadge = ({ success, label }: { success: boolean; label: string }) => (
    <Badge variant={success ? "default" : "destructive"} className="flex items-center gap-1">
      <StatusIcon success={success} />
      {label}
    </Badge>
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Authentication Debug</h1>
          <p className="text-gray-500 mt-2">
            Debug authentication and storage permissions
          </p>
        </div>
        <Button onClick={checkAuthStatus} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh Status
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Client-side Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Client-side Authentication
            </CardTitle>
            <CardDescription>
              Authentication status from React context
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">User Logged In:</span>
              <StatusBadge success={!!user} label={user ? "Yes" : "No"} />
            </div>
            
            {user && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Email:</span>
                  <span className="text-sm">{user.email}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">User ID:</span>
                  <span className="text-sm font-mono text-xs">{user.id.slice(0, 8)}...</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Is Admin:</span>
                  <StatusBadge success={user.isAdmin} label={user.isAdmin ? "Yes" : "No"} />
                </div>
                {supabaseUser?.email_confirmed_at && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Email Verified:</span>
                    <StatusBadge success={true} label="Yes" />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Server-side Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Server-side Authentication
            </CardTitle>
            <CardDescription>
              Authentication status from server APIs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {authStatus ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">User Session:</span>
                  <StatusBadge 
                    success={authStatus.details?.userAuth?.success} 
                    label={authStatus.details?.userAuth?.success ? "Valid" : "Invalid"} 
                  />
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Service Role:</span>
                  <StatusBadge 
                    success={authStatus.details?.serviceAuth?.success} 
                    label={authStatus.details?.serviceAuth?.success ? "Working" : "Failed"} 
                  />
                </div>

                {authStatus.details?.userAuth?.user && (
                  <div className="text-xs text-gray-500 mt-2">
                    Server User: {authStatus.details.userAuth.user.email}
                  </div>
                )}

                {authStatus.details?.serviceAuth?.error && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {authStatus.details.serviceAuth.error}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            ) : (
              <div className="text-sm text-gray-500">Loading...</div>
            )}
          </CardContent>
        </Card>

        {/* Storage Access */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Storage Access
            </CardTitle>
            <CardDescription>
              Supabase Storage bucket and access status
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {authStatus ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Storage API:</span>
                  <StatusBadge 
                    success={authStatus.details?.storageAccess?.success} 
                    label={authStatus.details?.storageAccess?.success ? "Accessible" : "Failed"} 
                  />
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">General Bucket:</span>
                  <StatusBadge 
                    success={authStatus.details?.storageAccess?.generalBucketExists} 
                    label={authStatus.details?.storageAccess?.generalBucketExists ? "Exists" : "Missing"} 
                  />
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Buckets:</span>
                  <span className="text-sm">{authStatus.details?.storageAccess?.totalBuckets || 0}</span>
                </div>

                {authStatus.details?.storageAccess?.buckets?.length > 0 && (
                  <div className="mt-2">
                    <span className="text-xs font-medium">Buckets:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {authStatus.details.storageAccess.buckets.map((bucket: any) => (
                        <Badge key={bucket.name} variant="outline" className="text-xs">
                          {bucket.name} {bucket.public && "(public)"}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-sm text-gray-500">Loading...</div>
            )}
          </CardContent>
        </Card>

        {/* Upload Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Permissions
            </CardTitle>
            <CardDescription>
              File upload capabilities and permissions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {authStatus ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Can Upload:</span>
                  <StatusBadge 
                    success={authStatus.details?.uploadPermissions?.canUpload} 
                    label={authStatus.details?.uploadPermissions?.canUpload ? "Yes" : "No"} 
                  />
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Upload Test:</span>
                  <StatusBadge 
                    success={authStatus.details?.uploadPermissions?.success} 
                    label={authStatus.details?.uploadPermissions?.success ? "Passed" : "Failed"} 
                  />
                </div>

                {authStatus.details?.uploadPermissions?.error && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {authStatus.details.uploadPermissions.error}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            ) : (
              <div className="text-sm text-gray-500">Loading...</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Overall Status */}
      {authStatus && (
        <Card>
          <CardHeader>
            <CardTitle>Overall Status</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className={authStatus.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
              <StatusIcon success={authStatus.success} />
              <AlertDescription className={authStatus.success ? "text-green-800" : "text-red-800"}>
                {authStatus.message}
              </AlertDescription>
            </Alert>

            {authStatus.recommendations?.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Recommendations:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  {authStatus.recommendations.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
